# Pentathlon Benchmarks CMake Configuration
cmake_minimum_required(VERSION 3.20)

# Build options for different linking configurations
option(PENTATHLON_LINK_SKIPLIST "Link with skiplist library (pentathlon-bm-target-skiplist)" ON)
option(PENTATHLON_LINK_COMPUTE "Link with runtime/compute library (dm_compiler_rt_compute)" OFF)
option(PENTATHLON_LINK_LRU "Link with LRU cache library (lru-cache)" OFF)
option(PENTATHLON_USE_OPTIMIZED_SKIPLIST "Use optimized skiplist assembly if available" OFF)

# Find required libraries
find_library(MATH_LIBRARIES m)
find_package(Threads REQUIRED)

# Note: We look for pre-built libraries in runtime/build instead of compiling them

# Print configuration
message(STATUS "=== Pentathlon Benchmark Configuration ===")
message(STATUS "Link with skiplist: ${PENTATHLON_LINK_SKIPLIST}")
message(STATUS "Link with compute: ${PENTATHLON_LINK_COMPUTE}")
message(STATUS "Link with LRU: ${PENTATHLON_LINK_LRU}")
message(STATUS "Use optimized skiplist: ${PENTATHLON_USE_OPTIMIZED_SKIPLIST}")
message(STATUS "==========================================")

# Conditional subdirectory inclusion
if(PENTATHLON_LINK_SKIPLIST)
    add_subdirectory(skiplist)
endif()

if(PENTATHLON_LINK_COMPUTE)
    # Look for pre-built libdm_compiler_rt_compute.a in runtime/build
    set(COMPUTE_LIB_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../runtime/build/compute/libdm_compiler_rt_compute.a")
    if(EXISTS "${COMPUTE_LIB_PATH}")
        # Create imported library target
        add_library(dm_compiler_rt_compute STATIC IMPORTED)
        set_target_properties(dm_compiler_rt_compute PROPERTIES
            IMPORTED_LOCATION "${COMPUTE_LIB_PATH}"
            INTERFACE_INCLUDE_DIRECTORIES "${CMAKE_CURRENT_SOURCE_DIR}/../runtime/compute/include"
        )
        message(STATUS "Found pre-built compute library: ${COMPUTE_LIB_PATH}")
    else()
        message(WARNING "Pre-built compute library not found at: ${COMPUTE_LIB_PATH}")
        message(WARNING "Build the runtime/compute module first, then copy libdm_compiler_rt_compute.a to runtime/build/")
        set(PENTATHLON_LINK_COMPUTE OFF)
    endif()
endif()

if(PENTATHLON_LINK_LRU)
    # Look for pre-built lru-cache library in runtime/build
    set(LRU_LIB_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../runtime/build/lru/liblru-cache.a")
    if(EXISTS "${LRU_LIB_PATH}")
        # Create imported library target
        add_library(lru-cache STATIC IMPORTED)
        set_target_properties(lru-cache PROPERTIES
            IMPORTED_LOCATION "${LRU_LIB_PATH}"
            INTERFACE_INCLUDE_DIRECTORIES "${CMAKE_CURRENT_SOURCE_DIR}/../runtime/lru/include"
        )
        message(STATUS "Found pre-built LRU library: ${LRU_LIB_PATH}")
    else()
        message(WARNING "Pre-built LRU library not found at: ${LRU_LIB_PATH}")
        message(WARNING "Build the runtime/lru module first, then copy liblru-cache.a to runtime/build/")
        set(PENTATHLON_LINK_LRU OFF)
    endif()
endif()

# Create optimized skiplist library from assembly if available
set(OPTIMIZED_SKIPLIST_ASM "${CMAKE_CURRENT_SOURCE_DIR}/scripts/output/skiplist.s")
if(EXISTS "${OPTIMIZED_SKIPLIST_ASM}")
    add_library(libskiplist_optimized STATIC ${OPTIMIZED_SKIPLIST_ASM})
    set_target_properties(libskiplist_optimized PROPERTIES
        LINKER_LANGUAGE CXX
        OUTPUT_NAME "skiplist_optimized"
    )
    message(STATUS "Created libskiplist_optimized.a from assembly: ${OPTIMIZED_SKIPLIST_ASM}")
    set(HAVE_OPTIMIZED_SKIPLIST TRUE)
else()
    message(STATUS "Optimized skiplist assembly not found at: ${OPTIMIZED_SKIPLIST_ASM}")
    message(STATUS "Run 'cd scripts && ./compile.sh' to generate optimized skiplist")
    set(HAVE_OPTIMIZED_SKIPLIST FALSE)
endif()

# Create benchmark generators library
add_library(pentathlon-bm-generators STATIC
    generators/splitmix64.c
    generators/xoshiro256p.c
    generators/zipf.c
)
target_include_directories(pentathlon-bm-generators PRIVATE .)
if(MATH_LIBRARIES)
    target_link_libraries(pentathlon-bm-generators ${MATH_LIBRARIES})
endif()

# Create benchmark workload library
add_library(pentathlon-bm-workload STATIC
    workload.c
)
target_include_directories(pentathlon-bm-workload PRIVATE .)
target_link_libraries(pentathlon-bm-workload
    pentathlon-bm-generators
    Threads::Threads
)

# Main benchmark executable (original)
add_executable(pentathlon-bm-local
    local.c
)
target_include_directories(pentathlon-bm-local PRIVATE .)

# Base libraries that are always linked
target_link_libraries(pentathlon-bm-local
    pentathlon-bm-workload
    pentathlon-bm-generators
)

# Math library
if(MATH_LIBRARIES)
    target_link_libraries(pentathlon-bm-local ${MATH_LIBRARIES})
endif()

# Conditional linking based on options for original executable
set(LINKED_COMPONENTS "")

if(PENTATHLON_LINK_SKIPLIST)
    if(PENTATHLON_USE_OPTIMIZED_SKIPLIST AND HAVE_OPTIMIZED_SKIPLIST)
        target_link_libraries(pentathlon-bm-local libskiplist_optimized)
        list(APPEND LINKED_COMPONENTS "skiplist (optimized)")
    else()
        target_link_libraries(pentathlon-bm-local pentathlon-bm-target-skiplist)
        list(APPEND LINKED_COMPONENTS "skiplist")
    endif()
endif()

if(PENTATHLON_LINK_COMPUTE)
    target_link_libraries(pentathlon-bm-local dm_compiler_rt_compute)
    target_include_directories(pentathlon-bm-local PRIVATE ../runtime/compute/include)
    list(APPEND LINKED_COMPONENTS "compute")
endif()

if(PENTATHLON_LINK_LRU)
    target_link_libraries(pentathlon-bm-local lru-cache)
    target_include_directories(pentathlon-bm-local PRIVATE ../runtime/lru/include)
    list(APPEND LINKED_COMPONENTS "lru")
endif()

# Print linked components
if(LINKED_COMPONENTS)
    string(REPLACE ";" ", " LINKED_COMPONENTS_STR "${LINKED_COMPONENTS}")
    message(STATUS "Linked components: ${LINKED_COMPONENTS_STR}")
else()
    message(STATUS "Linked components: generators, workload only")
endif()

# NEW TARGET: benchmark-optimized
# Links pentathlon-bm-local with libskiplist_optimized.a and libdm_compiler_rt_compute.a
add_executable(pentathlon-bm-local-optimized
    local.c
)
target_include_directories(pentathlon-bm-local-optimized PRIVATE .)

# Link with base libraries
target_link_libraries(pentathlon-bm-local-optimized
    pentathlon-bm-workload
    pentathlon-bm-generators
)

# Math library
if(MATH_LIBRARIES)
    target_link_libraries(pentathlon-bm-local-optimized ${MATH_LIBRARIES})
endif()

# Link with libskiplist_optimized.a
if(HAVE_OPTIMIZED_SKIPLIST)
    target_link_libraries(pentathlon-bm-local-optimized libskiplist_optimized)
    message(STATUS "benchmark-optimized: Linking with libskiplist_optimized.a")
else()
    message(WARNING "benchmark-optimized: libskiplist_optimized.a not available")
    message(WARNING "Run 'cd scripts && ./compile.sh' to generate optimized skiplist")
    # Fallback to regular skiplist if available
    if(TARGET pentathlon-bm-target-skiplist)
        target_link_libraries(pentathlon-bm-local-optimized pentathlon-bm-target-skiplist)
        message(STATUS "benchmark-optimized: Falling back to regular skiplist")
    endif()
endif()

# Link with libdm_compiler_rt_compute.a
if(TARGET dm_compiler_rt_compute)
    target_link_libraries(pentathlon-bm-local-optimized dm_compiler_rt_compute)
    target_include_directories(pentathlon-bm-local-optimized PRIVATE ../runtime/compute/include)
    message(STATUS "benchmark-optimized: Linking with libdm_compiler_rt_compute.a")
else()
    message(WARNING "benchmark-optimized: libdm_compiler_rt_compute.a not available")
    message(WARNING "Enable PENTATHLON_LINK_COMPUTE or check runtime/compute directory")
endif()

# Create the make target
add_custom_target(benchmark-optimized
    DEPENDS pentathlon-bm-local-optimized
    COMMENT "Building optimized benchmark with libskiplist_optimized.a and libdm_compiler_rt_compute.a"
)

# NEW TARGET: lru
# Links with libskiplist_optimized.a and lru-cache
add_executable(pentathlon-bm-local-lru
    local.c
)
target_include_directories(pentathlon-bm-local-lru PRIVATE .)

# Link with base libraries
target_link_libraries(pentathlon-bm-local-lru
    pentathlon-bm-workload
    pentathlon-bm-generators
)

# Math library
if(MATH_LIBRARIES)
    target_link_libraries(pentathlon-bm-local-lru ${MATH_LIBRARIES})
endif()

# Link with libskiplist_optimized.a
if(HAVE_OPTIMIZED_SKIPLIST)
    target_link_libraries(pentathlon-bm-local-lru libskiplist_optimized)
    message(STATUS "lru: Linking with libskiplist_optimized.a")
else()
    message(WARNING "lru: libskiplist_optimized.a not available")
    message(WARNING "Run 'cd scripts && ./compile.sh' to generate optimized skiplist")
    # Fallback to regular skiplist if available
    if(TARGET pentathlon-bm-target-skiplist)
        target_link_libraries(pentathlon-bm-local-lru pentathlon-bm-target-skiplist)
        message(STATUS "lru: Falling back to regular skiplist")
    endif()
endif()

# Link with lru-cache
if(TARGET lru-cache)
    target_link_libraries(pentathlon-bm-local-lru lru-cache)
    target_include_directories(pentathlon-bm-local-lru PRIVATE ../runtime/lru/include)
    message(STATUS "lru: Linking with lru-cache")
else()
    message(WARNING "lru: lru-cache not available")
    message(WARNING "Enable PENTATHLON_LINK_LRU or check runtime/lru directory")
endif()

# Create the make target
add_custom_target(lru
    DEPENDS pentathlon-bm-local-lru
    COMMENT "Building LRU benchmark with libskiplist_optimized.a and lru-cache"
)

# Additional custom targets for backward compatibility and convenience
add_custom_target(benchmark-skiplist
    COMMAND ${CMAKE_COMMAND} -DPENTATHLON_LINK_SKIPLIST=ON
                             -DPENTATHLON_LINK_COMPUTE=OFF
                             -DPENTATHLON_LINK_LRU=OFF
                             ${CMAKE_SOURCE_DIR}
    COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR} --target pentathlon-bm-local
    COMMENT "Building benchmark with skiplist only"
)

add_custom_target(benchmark-compute
    COMMAND ${CMAKE_COMMAND} -DPENTATHLON_LINK_SKIPLIST=OFF
                             -DPENTATHLON_LINK_COMPUTE=ON
                             -DPENTATHLON_LINK_LRU=OFF
                             ${CMAKE_SOURCE_DIR}
    COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR} --target pentathlon-bm-local
    COMMENT "Building benchmark with compute runtime only"
)

add_custom_target(benchmark-lru-original
    COMMAND ${CMAKE_COMMAND} -DPENTATHLON_LINK_SKIPLIST=OFF
                             -DPENTATHLON_LINK_COMPUTE=OFF
                             -DPENTATHLON_LINK_LRU=ON
                             ${CMAKE_SOURCE_DIR}
    COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR} --target pentathlon-bm-local
    COMMENT "Building benchmark with LRU cache only (original configuration)"
)

add_custom_target(benchmark-all
    COMMAND ${CMAKE_COMMAND} -DPENTATHLON_LINK_SKIPLIST=ON
                             -DPENTATHLON_LINK_COMPUTE=ON
                             -DPENTATHLON_LINK_LRU=ON
                             ${CMAKE_SOURCE_DIR}
    COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR} --target pentathlon-bm-local
    COMMENT "Building benchmark with all components"
)

# Compiler flags for all executables
set(COMMON_COMPILE_OPTIONS
    $<$<COMPILE_LANGUAGE:C>:-Wall -Wextra>
    $<$<COMPILE_LANGUAGE:CXX>:-Wall -Wextra>
)

target_compile_options(pentathlon-bm-local PRIVATE ${COMMON_COMPILE_OPTIONS})
target_compile_options(pentathlon-bm-local-optimized PRIVATE ${COMMON_COMPILE_OPTIONS})
target_compile_options(pentathlon-bm-local-lru PRIVATE ${COMMON_COMPILE_OPTIONS})

# Debug/Release specific flags
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(DEBUG_OPTIONS -g -O0)
    set(DEBUG_DEFINITIONS DEBUG=1)

    target_compile_options(pentathlon-bm-local PRIVATE ${DEBUG_OPTIONS})
    target_compile_options(pentathlon-bm-local-optimized PRIVATE ${DEBUG_OPTIONS})
    target_compile_options(pentathlon-bm-local-lru PRIVATE ${DEBUG_OPTIONS})

    target_compile_definitions(pentathlon-bm-local PRIVATE ${DEBUG_DEFINITIONS})
    target_compile_definitions(pentathlon-bm-local-optimized PRIVATE ${DEBUG_DEFINITIONS})
    target_compile_definitions(pentathlon-bm-local-lru PRIVATE ${DEBUG_DEFINITIONS})
else()
    set(RELEASE_OPTIONS -O2 -DNDEBUG)

    target_compile_options(pentathlon-bm-local PRIVATE ${RELEASE_OPTIONS})
    target_compile_options(pentathlon-bm-local-optimized PRIVATE ${RELEASE_OPTIONS})
    target_compile_options(pentathlon-bm-local-lru PRIVATE ${RELEASE_OPTIONS})
endif()

# Install targets
install(TARGETS pentathlon-bm-local pentathlon-bm-local-optimized pentathlon-bm-local-lru
    RUNTIME DESTINATION bin
)

# Configuration summary
message(STATUS "")
message(STATUS "=== Build Configuration Summary ===")
message(STATUS "Main executable: pentathlon-bm-local")
message(STATUS "Optimized executable: pentathlon-bm-local-optimized")
message(STATUS "LRU executable: pentathlon-bm-local-lru")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "Optimized skiplist available: ${HAVE_OPTIMIZED_SKIPLIST}")
message(STATUS "")
message(STATUS "Available make targets:")
message(STATUS "  pentathlon-bm-local          - Standard benchmark (configurable)")
message(STATUS "  benchmark-optimized          - libskiplist_optimized.a + libdm_compiler_rt_compute.a")
message(STATUS "  lru                          - libskiplist_optimized.a + lru-cache")
message(STATUS "  benchmark-skiplist           - Skiplist only")
message(STATUS "  benchmark-compute            - Compute runtime only")
message(STATUS "  benchmark-lru-original       - LRU cache only (original)")
message(STATUS "  benchmark-all                - All components")
message(STATUS "")
message(STATUS "Usage examples:")
message(STATUS "  make benchmark-optimized     # Build optimized benchmark")
message(STATUS "  make lru                     # Build LRU benchmark")
message(STATUS "  make benchmark-all           # Build with all components")
message(STATUS "")
message(STATUS "CMake configuration examples:")
message(STATUS "  cmake -DPENTATHLON_LINK_SKIPLIST=ON ..")
message(STATUS "  cmake -DPENTATHLON_LINK_COMPUTE=ON ..")
message(STATUS "  cmake -DPENTATHLON_LINK_LRU=ON ..")
message(STATUS "=====================================")
