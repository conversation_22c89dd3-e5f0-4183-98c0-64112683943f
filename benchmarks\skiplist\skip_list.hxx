#pragma once

#include "list.hxx"
#include "node.hxx"
// #include <map>

class SkipList {
   private:
    // Head and Tail of the Skiplist
    Node *head;
    Node *tail;

   public:
    SkipList();
    SkipList(int max_elements, float probability);
    ~SkipList();
    int get_random_level();

    // Supported operations
    int find(int key, Node **predecessors, Node **successors);
    bool add(int key, const uint8_t* value = nullptr);
    const uint8_t* search(int key);
    bool remove(int key);
    // std::map<int, uint8_t*> range(int start_key, int end_key);
    void display();
};
