#include "bplus_tree.h"

int main() {
    printf("B+ Tree Split Test\n");
    printf("==================\n");

    // Create a tree that will force splits
    BPlusTree *tree = bplus_tree_create();
    if (!tree) {
        printf("Failed to create tree\n");
        return 1;
    }

    printf("Created tree with order 4\n");

    // Insert keys that will cause splits
    printf("Inserting keys to force splits: 10, 20, 5, 6\n");

    printf("Insert 10: ");
    if (bplus_tree_insert(tree, 10, 100))
        printf("Success\n");
    else
        printf("Failed\n");
    bplus_tree_print(tree);

    printf("Insert 20: ");
    if (bplus_tree_insert(tree, 20, 200))
        printf("Success\n");
    else
        printf("Failed\n");
    bplus_tree_print(tree);

    printf("Insert 5: ");
    if (bplus_tree_insert(tree, 5, 50))
        printf("Success\n");
    else
        printf("Failed\n");
    bplus_tree_print(tree);

    printf("Insert 6 (should cause split): ");
    if (bplus_tree_insert(tree, 6, 60))
        printf("Success\n");
    else
        printf("Failed\n");
    bplus_tree_print(tree);

    // Test searches
    printf("\nTesting searches:\n");
    int keys[] = {5, 6, 10, 20};
    for (int i = 0; i < 4; i++) {
        int value;
        printf("Search %d: ", keys[i]);
        if (bplus_tree_search(tree, keys[i], &value)) {
            printf("Found! Value: %d\n", value);
        } else {
            printf("Not found\n");
        }
    }

    printf("\nLeaf traversal:\n");
    bplus_tree_traverse_leaves(tree);

    printf("\nDestroying tree...\n");
    bplus_tree_destroy(tree);
    printf("Test completed successfully\n");

    return 0;
}
