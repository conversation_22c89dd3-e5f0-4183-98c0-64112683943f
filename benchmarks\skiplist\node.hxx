#pragma once

#include <mutex>
#include "key_value_pair.hxx"
#include "list.hxx"

struct Node {
    // Stores the key and value for the Node
    KeyValuePair key_value_pair;

    // Stores the reference of the next node until the top level for the node
    MyList<Node*> next;

    // Lock to lock the node when modifing it
    std::mutex node_lock;

    // Boolean variable to be marked if this Node is being deleted
    bool marked = false;

    // Boolean variable to indicate the Node is completely linked to predecessors
    // and successors
    bool fully_linked = false;

    // The Maximum level until which the node is available
    int top_level;

    Node() {}
    Node(int key, const uint8_t* value, int level) : key_value_pair(KeyValuePair(key, value)) {
        for (size_t i = 0; i < level + 1; i++) {
            next.push(nullptr);
        }
        top_level = level;
    }
    ~Node() {}

    int get_key() { return key_value_pair.get_key(); }
    const uint8_t* get_value() { return key_value_pair.get_value(); }

    void lock() { node_lock.lock(); }
    void unlock() { node_lock.unlock(); }
};
