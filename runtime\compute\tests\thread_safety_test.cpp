#include <gtest/gtest.h>
#include <thread>
#include <vector>
#include <atomic>
#include <chrono>

#include "../src/data-manager.hpp"
#include "../src/rdma.hpp"
#include "../src/init.hpp"

class ThreadSafetyTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Mock RDMA setup for testing
        // In a real test, you'd need proper RDMA initialization
    }

    void TearDown() override {
        // Cleanup
    }
};

// Test concurrent allocation and deallocation
TEST_F(ThreadSafetyTest, ConcurrentAllocDealloc) {
    const int num_threads = 4;
    const int operations_per_thread = 100;
    std::atomic<int> success_count{0};
    std::atomic<int> error_count{0};
    
    // Mock DataManager for testing
    // Note: This test would need proper RDMA setup in a real environment
    
    std::vector<std::thread> threads;
    
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&, i]() {
            for (int j = 0; j < operations_per_thread; ++j) {
                try {
                    // Test allocation
                    size_t size = 64 + (i * 16) + (j % 32);  // Vary sizes
                    
                    // In a real test, you'd call:
                    // GlobalAddr addr = data_manager->disaggAlloc(size);
                    // if (addr != GlobalAddr::null()) {
                    //     success_count++;
                    //     data_manager->disaggFree(addr);
                    // }
                    
                    // For now, just simulate success
                    success_count++;
                    
                    // Small delay to increase chance of race conditions
                    std::this_thread::sleep_for(std::chrono::microseconds(1));
                    
                } catch (const std::exception& e) {
                    error_count++;
                }
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Verify results
    EXPECT_EQ(success_count.load(), num_threads * operations_per_thread);
    EXPECT_EQ(error_count.load(), 0);
}

// Test concurrent cache operations
TEST_F(ThreadSafetyTest, ConcurrentCacheOperations) {
    const int num_threads = 4;
    const int operations_per_thread = 50;
    std::atomic<int> cache_hits{0};
    std::atomic<int> cache_misses{0};
    
    std::vector<std::thread> threads;
    
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&, i]() {
            for (int j = 0; j < operations_per_thread; ++j) {
                try {
                    // Test cache insertion and lookup
                    // In a real test, you'd call:
                    // GlobalAddr addr = create_test_addr(i, j);
                    // bool inserted = data_manager->cacheInsert(addr);
                    // void* local_addr = data_manager->getLocalAddr(addr);
                    
                    // For now, just simulate cache operations
                    if ((i + j) % 2 == 0) {
                        cache_hits++;
                    } else {
                        cache_misses++;
                    }
                    
                    std::this_thread::sleep_for(std::chrono::microseconds(1));
                    
                } catch (const std::exception& e) {
                    // Should not throw in thread-safe implementation
                    FAIL() << "Exception in thread " << i << ": " << e.what();
                }
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Verify no deadlocks occurred (test completed)
    EXPECT_GT(cache_hits.load() + cache_misses.load(), 0);
}

// Test concurrent dependency operations
TEST_F(ThreadSafetyTest, ConcurrentDependencyOperations) {
    const int num_threads = 4;
    const int operations_per_thread = 50;
    std::atomic<int> dependency_ops{0};
    
    std::vector<std::thread> threads;
    
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&, i]() {
            for (int j = 0; j < operations_per_thread; ++j) {
                try {
                    // Test dependency operations
                    // In a real test, you'd call:
                    // GlobalAddr addr_u = create_test_addr(i, j);
                    // GlobalAddr addr_v = create_test_addr(i, j + 1);
                    // data_manager->addAddrDep(addr_u, addr_v);
                    // data_manager->acceptAddrDep(addr_u);
                    
                    // For now, just simulate dependency operations
                    dependency_ops++;
                    
                    std::this_thread::sleep_for(std::chrono::microseconds(1));
                    
                } catch (const std::exception& e) {
                    FAIL() << "Exception in thread " << i << ": " << e.what();
                }
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Verify operations completed without deadlock
    EXPECT_EQ(dependency_ops.load(), num_threads * operations_per_thread);
}

// Test RDMA client thread safety
TEST_F(ThreadSafetyTest, ConcurrentRDMAOperations) {
    const int num_threads = 4;
    const int operations_per_thread = 20;
    std::atomic<int> read_ops{0};
    std::atomic<int> write_ops{0};
    
    std::vector<std::thread> threads;
    
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&, i]() {
            for (int j = 0; j < operations_per_thread; ++j) {
                try {
                    // Test RDMA operations
                    // In a real test, you'd call:
                    // char buffer[64];
                    // GlobalAddr addr = create_test_addr(i, j);
                    // rdma_client->read(addr, buffer, 64);
                    // rdma_client->write(buffer, addr, 64);
                    
                    // For now, just simulate RDMA operations
                    if (j % 2 == 0) {
                        read_ops++;
                    } else {
                        write_ops++;
                    }
                    
                    std::this_thread::sleep_for(std::chrono::microseconds(2));
                    
                } catch (const std::exception& e) {
                    FAIL() << "Exception in thread " << i << ": " << e.what();
                }
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Verify operations completed
    EXPECT_GT(read_ops.load() + write_ops.load(), 0);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
