#pragma once

#include <cstdint>
#include <cstring>

class KeyValuePair {
   private:
    int key;
    uint8_t value[1024];  // Fixed 1KB value

   public:
    KeyValuePair() : key(0) {
        memset(value, 0, sizeof(value));
    }
    KeyValuePair(int key, const uint8_t* value_data = nullptr) : key(key) {
        if (value_data) {
            memcpy(value, value_data, sizeof(value));
        } else {
            memset(value, 0, sizeof(value));
        }
    }
    ~KeyValuePair() {}

    int get_key() const { return key; }
    const uint8_t* get_value() const { return value; }
    uint8_t* get_value() { return value; }

    void set_key(int new_key) { key = new_key; }
    void set_value(const uint8_t* value_data) {
        if (value_data) {
            memcpy(value, value_data, sizeof(value));
        } else {
            memset(value, 0, sizeof(value));
        }
    }
};
