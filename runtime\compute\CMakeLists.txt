add_library(dm_compiler_rt_compute
    src/addr.cpp
    src/alloc.cpp
    src/data-manager.cpp
    src/init.cpp
    src/rdma.c
    src/rdma.cpp
)
target_include_directories(dm_compiler_rt_compute
    PUBLIC
        include
        ${IBVERBS_INCLUDE_DIRS}
    # PRIVATE
        src
)

# find libskiplist in ../../benchmarks/scripts/output/libskiplist_optimized.a
find_library(SKIPLIST_LIBRARIES
    NAMES libskiplist_optimized.a
    PATHS ../../benchmarks/scripts/output
    NO_DEFAULT_PATH
)

# find libbptree in ../../benchmarks/scripts/output/libbptree.a
find_library(BPTREE_LIBRARIES
    NAMES libbptree.a
    PATHS ../../benchmarks/scripts/output
    NO_DEFAULT_PATH
)

target_link_libraries(dm_compiler_rt_compute
    ${IBVERBS_LIBRARIES}
    RDMA::RDMAcm
    Threads::Threads
)

add_executable(dm_compiler_rt_compute_tests
    tests/main.cpp
)
target_include_directories(dm_compiler_rt_compute_tests
    PRIVATE
        tests
)
# Link with threading support for multithreaded test
target_link_libraries(dm_compiler_rt_compute_tests
    dm_compiler_rt_compute
    Threads::Threads
)

# C version tester
add_executable(dm_compiler_rt_compute_tests_c
    tests/main_c.c
)
target_include_directories(dm_compiler_rt_compute_tests_c
    PRIVATE
        tests
)
# Link with threading support for multithreaded test (C version)
target_link_libraries(dm_compiler_rt_compute_tests_c
    dm_compiler_rt_compute
    Threads::Threads
)

# CMake Error: Cannot determine link language for target "libskiplist_optimized"

# Create optimized skiplist library from assembly if available, language is C++

# add_library(libskiplist_optimized
#     ../../benchmarks/scripts/output/skiplist.s
# )

# use libskiplist in ../../benchmarks/scripts/output/libskiplist_optimized.a

add_executable(benchmark-skiplist
    ../../benchmarks/local.c
    ../../benchmarks/workload.c
    ../../benchmarks/generators/splitmix64.c
    ../../benchmarks/generators/xoshiro256p.c
    ../../benchmarks/generators/zipf.c
)

# link with libskiplist in ../../benchmarks/scripts/output/libskiplist_optimized.a
target_link_libraries(benchmark-skiplist
    ${SKIPLIST_LIBRARIES}
    dm_compiler_rt_compute
    $<$<CXX_COMPILER_ID:GNU>:-lstdc++>
    $<$<CXX_COMPILER_ID:Clang>:-lc++>
)

add_executable(benchmark-bptree
    ../../benchmarks/local.c
    ../../benchmarks/workload.c
    ../../benchmarks/generators/splitmix64.c
    ../../benchmarks/generators/xoshiro256p.c
    ../../benchmarks/generators/zipf.c
)

# link with libbptree in ../../benchmarks/scripts/output/libbptree.a
target_link_libraries(benchmark-bptree
    ${BPTREE_LIBRARIES}
    dm_compiler_rt_compute
    $<$<CXX_COMPILER_ID:GNU>:-lstdc++>
    $<$<CXX_COMPILER_ID:Clang>:-lc++>
)
