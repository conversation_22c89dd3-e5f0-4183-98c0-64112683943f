#include "bplus_tree.h"

// Create a new B+ tree
BPlusTree *bplus_tree_create() {
    BPlusTree *tree = malloc(sizeof(BPlusTree));
    if (!tree) {
        printf("Error: Memory allocation failed\n");
        return NULL;
    }

    tree->root = NULL;
    tree->height = 0;
    tree->num_nodes = 0;

    // Initialize locks
    if (pthread_rwlock_init(&tree->tree_lock, NULL) != 0) {
        printf("Error: Failed to initialize tree rwlock\n");
        free(tree);
        return NULL;
    }

    if (pthread_mutex_init(&tree->root_mutex, NULL) != 0) {
        printf("Error: Failed to initialize root mutex\n");
        pthread_rwlock_destroy(&tree->tree_lock);
        free(tree);
        return NULL;
    }

    return tree;
}

// Create a new node
BPlusNode *bplus_node_create(NodeType type) {
    BPlusNode *node = malloc(sizeof(BPlusNode));
    if (!node) {
        printf("Error: Memory allocation failed\n");
        return NULL;
    }

    node->type = type;
    node->num_keys = 0;
    node->parent = NULL;
    node->next = NULL;
    node->is_deleted = false;

    // Initialize node lock
    if (pthread_rwlock_init(&node->rwlock, NULL) != 0) {
        printf("Error: Failed to initialize node rwlock\n");
        free(node);
        return NULL;
    }

    if (type == INTERNAL_NODE) {
        // Internal nodes have order+1 children (extra space for temporary overflow during splits)
        node->values = NULL;
        // Initialize children pointers
        for (int i = 0; i < DEFAULT_ORDER + 1; i++) {
            node->children[i] = NULL;
        }
    } else {
        // Leaf nodes have values (extra space for temporary overflow during splits)
        node->values = malloc(sizeof(int) * DEFAULT_ORDER);
        if (!node->values) {
            free(node);
            return NULL;
        }
        // Initialize values
        for (int i = 0; i < DEFAULT_ORDER; i++) {
            node->values[i] = 0;
        }
    }

    return node;
}

// Destroy a node and its subtree
void bplus_node_destroy(BPlusNode *node) {
    if (!node) return;

    // Mark as deleted first
    node->is_deleted = true;

    if (node->type == INTERNAL_NODE) {
        for (int i = 0; i <= node->num_keys; i++) {
            bplus_node_destroy(node->children[i]);
        }
    }

    if (node->values) {
        free(node->values);
    }

    // Destroy the lock
    pthread_rwlock_destroy(&node->rwlock);

    free(node);
}

// Destroy the entire tree
void bplus_tree_destroy(BPlusTree *tree) {
    if (!tree) return;

    // Acquire write lock to ensure no concurrent operations
    pthread_rwlock_wrlock(&tree->tree_lock);

    bplus_node_destroy(tree->root);

    // Destroy locks
    pthread_rwlock_unlock(&tree->tree_lock);
    pthread_rwlock_destroy(&tree->tree_lock);
    pthread_mutex_destroy(&tree->root_mutex);

    free(tree);
}

// Check if a node is full
bool bplus_node_is_full(BPlusNode *node) {
    if (!node) return false;
    return node->num_keys >= (DEFAULT_ORDER - 1);
}

// Check if a node has minimal number of keys
bool bplus_node_is_minimal(BPlusNode *node) {
    if (!node) return false;
    int min_keys = (DEFAULT_ORDER - 1) / 2;
    return node->num_keys >= min_keys;
}

// Find the index where a key should be inserted or is located
int bplus_node_find_key_index(BPlusNode *node, int key) {
    if (!node) return -1;

    int i = 0;
    while (i < node->num_keys && node->keys[i] < key) {
        i++;
    }
    return i;
}

// Shift keys and values to the right starting from start_index
void bplus_node_shift_keys_right(BPlusNode *node, int start_index) {
    if (!node || start_index < 0) return;

    // Shift keys
    for (int i = node->num_keys; i > start_index; i--) {
        node->keys[i] = node->keys[i - 1];
    }

    if (node->type == LEAF_NODE) {
        // Shift values for leaf nodes
        for (int i = node->num_keys; i > start_index; i--) {
            node->values[i] = node->values[i - 1];
        }
    } else {
        // Shift children for internal nodes
        for (int i = node->num_keys + 1; i > start_index + 1; i--) {
            node->children[i] = node->children[i - 1];
        }
    }
}

// Find the leaf node where a key should be inserted or is located
BPlusNode *bplus_tree_find_leaf(BPlusTree *tree, int key) {
    if (!tree || !tree->root) return NULL;

    BPlusNode *current = tree->root;

    while (current->type == INTERNAL_NODE) {
        int index = bplus_node_find_key_index(current, key);
        // For internal nodes, if key is greater than or equal to the key at index,
        // we should go to the right child (index + 1), otherwise go to left child (index)
        if (index < current->num_keys && key >= current->keys[index]) {
            index++;
        }
        current = current->children[index];
    }

    return current;
}

// Insert a key-value pair into a leaf node
void bplus_node_insert_key_value(BPlusNode *leaf, int key, int value) {
    if (!leaf || leaf->type != LEAF_NODE) return;

    int index = bplus_node_find_key_index(leaf, key);

    // Check if key already exists
    if (index < leaf->num_keys && leaf->keys[index] == key) {
        leaf->values[index] = value;  // Update existing value
        return;
    }

    // Shift elements to make room
    bplus_node_shift_keys_right(leaf, index);

    // Insert new key-value pair
    leaf->keys[index] = key;
    leaf->values[index] = value;
    leaf->num_keys++;
}

// Insert a key and child pointer into an internal node
void bplus_node_insert_key_child(BPlusNode *internal, int key, BPlusNode *child) {
    if (!internal || internal->type != INTERNAL_NODE) return;

    int index = bplus_node_find_key_index(internal, key);

    // Shift elements to make room
    bplus_node_shift_keys_right(internal, index);

    // Insert new key and update child pointer
    internal->keys[index] = key;
    internal->children[index + 1] = child;
    if (child) {
        child->parent = internal;
    }
    internal->num_keys++;
}

// Split a leaf node
BPlusNode *bplus_node_split_leaf(BPlusNode *leaf) {
    if (!leaf || leaf->type != LEAF_NODE) return NULL;

    int split_index = DEFAULT_ORDER / 2;

    // Create new leaf node
    BPlusNode *new_leaf = bplus_node_create(LEAF_NODE);
    if (!new_leaf) return NULL;

    // Copy half of the keys and values to the new leaf
    new_leaf->num_keys = leaf->num_keys - split_index;
    for (int i = 0; i < new_leaf->num_keys; i++) {
        new_leaf->keys[i] = leaf->keys[split_index + i];
        new_leaf->values[i] = leaf->values[split_index + i];
    }

    // Update the original leaf
    leaf->num_keys = split_index;

    // Link the leaf nodes
    new_leaf->next = leaf->next;
    leaf->next = new_leaf;

    // Set parent
    new_leaf->parent = leaf->parent;

    return new_leaf;
}

// Split an internal node
BPlusNode *bplus_node_split_internal(BPlusNode *internal) {
    if (!internal || internal->type != INTERNAL_NODE) return NULL;

    int split_index = DEFAULT_ORDER / 2;

    // Create new internal node
    BPlusNode *new_internal = bplus_node_create(INTERNAL_NODE);
    if (!new_internal) return NULL;

    // Copy half of the keys and children to the new internal node
    new_internal->num_keys = internal->num_keys - split_index - 1;
    for (int i = 0; i < new_internal->num_keys; i++) {
        new_internal->keys[i] = internal->keys[split_index + 1 + i];
    }

    for (int i = 0; i <= new_internal->num_keys; i++) {
        new_internal->children[i] = internal->children[split_index + 1 + i];
        if (new_internal->children[i]) {
            new_internal->children[i]->parent = new_internal;
        }
    }

    // Update the original internal node
    internal->num_keys = split_index;

    // Set parent
    new_internal->parent = internal->parent;

    return new_internal;
}

// Insert into parent after a split
void bplus_tree_insert_into_parent(BPlusTree *tree, BPlusNode *left, int key, BPlusNode *right) {
    if (!tree || !left || !right) return;

    BPlusNode *parent = left->parent;

    // If left node is root, create new root
    if (!parent) {
        BPlusNode *new_root = bplus_node_create(INTERNAL_NODE);
        if (!new_root) return;

        new_root->keys[0] = key;
        new_root->children[0] = left;
        new_root->children[1] = right;
        new_root->num_keys = 1;

        left->parent = new_root;
        right->parent = new_root;

        tree->root = new_root;
        tree->height++;
        return;
    }

    // Insert key into parent
    if (!bplus_node_is_full(parent)) {
        bplus_node_insert_key_child(parent, key, right);
    } else {
        // Parent is full, need to split
        bplus_node_insert_key_child(parent, key, right);

        // Get the middle key before splitting
        int split_index = DEFAULT_ORDER / 2;
        int promote_key = parent->keys[split_index];

        BPlusNode *new_parent = bplus_node_split_internal(parent);

        bplus_tree_insert_into_parent(tree, parent, promote_key, new_parent);
    }
}

// Search for a key in the tree
bool bplus_tree_search(BPlusTree *tree, int key, int *value) {
    if (!tree || !tree->root) return false;

    BPlusNode *leaf = bplus_tree_find_leaf(tree, key);
    if (!leaf) return false;

    int index = bplus_node_find_key_index(leaf, key);
    if (index < leaf->num_keys && leaf->keys[index] == key) {
        if (value) {
            *value = leaf->values[index];
        }
        return true;
    }

    return false;
}

// Insert a key-value pair into the tree
bool bplus_tree_insert(BPlusTree *tree, int key, int value) {
    if (!tree) return false;

    // If tree is empty, create root leaf node
    if (!tree->root) {
        tree->root = bplus_node_create(LEAF_NODE);
        if (!tree->root) return false;

        tree->root->keys[0] = key;
        tree->root->values[0] = value;
        tree->root->num_keys = 1;
        tree->height = 1;
        tree->num_nodes = 1;
        return true;
    }

    // Find the leaf node where the key should be inserted
    BPlusNode *leaf = bplus_tree_find_leaf(tree, key);
    if (!leaf) return false;

    // If leaf is not full, simply insert
    if (!bplus_node_is_full(leaf)) {
        bplus_node_insert_key_value(leaf, key, value);
        return true;
    }

    // Leaf is full, need to split
    bplus_node_insert_key_value(leaf, key, value);

    BPlusNode *new_leaf = bplus_node_split_leaf(leaf);
    if (!new_leaf) return false;

    tree->num_nodes++;

    // The first key of the new leaf goes up to the parent
    int promote_key = new_leaf->keys[0];

    bplus_tree_insert_into_parent(tree, leaf, promote_key, new_leaf);

    return true;
}

// Traverse and print all leaf nodes (in-order)
void bplus_tree_traverse_leaves(BPlusTree *tree) {
    if (!tree || !tree->root) {
        printf("Tree is empty\n");
        return;
    }

    // Find the leftmost leaf
    BPlusNode *current = tree->root;
    while (current->type == INTERNAL_NODE) {
        current = current->children[0];
    }

    // Traverse all leaf nodes
    printf("Leaf traversal: ");
    while (current) {
        for (int i = 0; i < current->num_keys; i++) {
            printf("(%d:%d) ", current->keys[i], current->values[i]);
        }
        current = current->next;
    }
    printf("\n");
}

// Print the tree structure
void bplus_tree_print_tree(BPlusNode *node, int level) {
    if (!node) return;

    // Print indentation
    for (int i = 0; i < level; i++) {
        printf("  ");
    }

    // Print node type and keys
    printf("%s: [", (node->type == LEAF_NODE) ? "LEAF" : "INTERNAL");
    for (int i = 0; i < node->num_keys; i++) {
        printf("%d", node->keys[i]);
        if (node->type == LEAF_NODE) {
            printf(":%d", node->values[i]);
        }
        if (i < node->num_keys - 1) printf(", ");
    }
    printf("]\n");

    // Recursively print children for internal nodes
    if (node->type == INTERNAL_NODE) {
        for (int i = 0; i <= node->num_keys; i++) {
            bplus_tree_print_tree(node->children[i], level + 1);
        }
    }
}

// Print the entire tree
void bplus_tree_print(BPlusTree *tree) {
    if (!tree) {
        printf("Tree is NULL\n");
        return;
    }

    printf("B+ Tree (order=%d, height=%d, nodes=%d):\n", DEFAULT_ORDER, tree->height,
           tree->num_nodes);

    if (!tree->root) {
        printf("Tree is empty\n");
        return;
    }

    bplus_tree_print_tree(tree->root, 0);
    printf("\n");
}

// Shift keys and values to the left starting from start_index
void bplus_node_shift_keys_left(BPlusNode *node, int start_index) {
    if (!node || start_index < 0) return;

    // Shift keys
    for (int i = start_index; i < node->num_keys - 1; i++) {
        node->keys[i] = node->keys[i + 1];
    }

    if (node->type == LEAF_NODE) {
        // Shift values for leaf nodes
        for (int i = start_index; i < node->num_keys - 1; i++) {
            node->values[i] = node->values[i + 1];
        }
    } else {
        // Shift children for internal nodes
        for (int i = start_index + 1; i < node->num_keys; i++) {
            node->children[i] = node->children[i + 1];
        }
    }
}

// Simple delete function (basic implementation)
bool bplus_tree_delete(BPlusTree *tree, int key) {
    if (!tree || !tree->root) return false;

    BPlusNode *leaf = bplus_tree_find_leaf(tree, key);
    if (!leaf) return false;

    int index = bplus_node_find_key_index(leaf, key);
    if (index >= leaf->num_keys || leaf->keys[index] != key) {
        return false;  // Key not found
    }

    // Remove the key-value pair
    bplus_node_shift_keys_left(leaf, index);
    leaf->num_keys--;

    // Note: This is a simplified delete that doesn't handle underflow
    // A complete implementation would need to handle merging and redistribution

    return true;
}

// ============================================================================
// CONCURRENT OPERATIONS USING LATCH CRABBING
// ============================================================================

// Lock a node with specified mode
void bplus_node_lock(BPlusNode *node, LockMode mode) {
    if (!node || node->is_deleted) return;

    switch (mode) {
        case LOCK_READ:
            pthread_rwlock_rdlock(&node->rwlock);
            break;
        case LOCK_WRITE:
            pthread_rwlock_wrlock(&node->rwlock);
            break;
        case LOCK_NONE:
            break;
    }
}

// Unlock a node
void bplus_node_unlock(BPlusNode *node) {
    if (!node || node->is_deleted) return;
    pthread_rwlock_unlock(&node->rwlock);
}

// Try to lock a node (non-blocking)
bool bplus_node_try_lock(BPlusNode *node, LockMode mode) {
    if (!node || node->is_deleted) return false;

    int result;
    switch (mode) {
        case LOCK_READ:
            result = pthread_rwlock_tryrdlock(&node->rwlock);
            break;
        case LOCK_WRITE:
            result = pthread_rwlock_trywrlock(&node->rwlock);
            break;
        case LOCK_NONE:
            return true;
    }

    return result == 0;
}

// Release all locks in the array
void bplus_release_locks(BPlusNode **locked_nodes, int num_locked) {
    for (int i = 0; i < num_locked; i++) {
        if (locked_nodes[i]) {
            bplus_node_unlock(locked_nodes[i]);
            locked_nodes[i] = NULL;
        }
    }
}

// Check if a node is safe for insert (won't cause split)
bool bplus_node_is_safe_for_insert(BPlusNode *node) {
    if (!node) return false;
    return !bplus_node_is_full(node);
}

// Check if a node is safe for delete (won't cause underflow)
bool bplus_node_is_safe_for_delete(BPlusNode *node) {
    if (!node) return false;
    // For simplified delete, we consider all nodes safe
    // In a complete implementation, this would check for underflow
    return true;
}

// Concurrent find leaf using latch crabbing
BPlusNode *bplus_tree_find_leaf_concurrent(BPlusTree *tree, int key, BPlusNode **locked_nodes,
                                           int *num_locked) {
    if (!tree || !tree->root) return NULL;

    *num_locked = 0;

    // Start with root
    pthread_mutex_lock(&tree->root_mutex);
    BPlusNode *current = tree->root;
    if (!current) {
        pthread_mutex_unlock(&tree->root_mutex);
        return NULL;
    }

    // Lock root for read
    bplus_node_lock(current, LOCK_READ);
    locked_nodes[(*num_locked)++] = current;
    pthread_mutex_unlock(&tree->root_mutex);

    // Traverse down the tree using latch crabbing
    while (current && current->type == INTERNAL_NODE) {
        int index = bplus_node_find_key_index(current, key);
        if (index < current->num_keys && key >= current->keys[index]) {
            index++;
        }

        BPlusNode *child = current->children[index];
        if (!child) break;

        // Lock child
        bplus_node_lock(child, LOCK_READ);
        locked_nodes[(*num_locked)++] = child;

        // Release parent lock (latch crabbing)
        bplus_node_unlock(locked_nodes[0]);

        // Shift locked nodes array
        for (int i = 0; i < *num_locked - 1; i++) {
            locked_nodes[i] = locked_nodes[i + 1];
        }
        (*num_locked)--;

        current = child;
    }

    return current;
}

// Concurrent search operation
bool bplus_tree_concurrent_search(BPlusTree *tree, int key, int *value) {
    if (!tree) return false;

    BPlusNode *locked_nodes[32];  // Max tree height assumption
    int num_locked = 0;

    // Acquire tree read lock
    pthread_rwlock_rdlock(&tree->tree_lock);

    BPlusNode *leaf = bplus_tree_find_leaf_concurrent(tree, key, locked_nodes, &num_locked);

    bool found = false;
    if (leaf) {
        int index = bplus_node_find_key_index(leaf, key);
        if (index < leaf->num_keys && leaf->keys[index] == key) {
            if (value) {
                *value = leaf->values[index];
            }
            found = true;
        }
    }

    // Release all locks
    bplus_release_locks(locked_nodes, num_locked);
    pthread_rwlock_unlock(&tree->tree_lock);

    return found;
}

// Concurrent insert operation using latch crabbing
bool bplus_tree_concurrent_insert(BPlusTree *tree, int key, int value) {
    if (!tree) return false;

    // Acquire tree read lock initially
    pthread_rwlock_rdlock(&tree->tree_lock);

    // Handle empty tree case
    pthread_mutex_lock(&tree->root_mutex);
    if (!tree->root) {
        pthread_mutex_unlock(&tree->root_mutex);
        pthread_rwlock_unlock(&tree->tree_lock);

        // Upgrade to write lock for structural change
        pthread_rwlock_wrlock(&tree->tree_lock);
        pthread_mutex_lock(&tree->root_mutex);

        // Double-check after acquiring write lock
        if (!tree->root) {
            tree->root = bplus_node_create(LEAF_NODE);
            if (!tree->root) {
                pthread_mutex_unlock(&tree->root_mutex);
                pthread_rwlock_unlock(&tree->tree_lock);
                return false;
            }

            tree->root->keys[0] = key;
            tree->root->values[0] = value;
            tree->root->num_keys = 1;
            tree->height = 1;
            tree->num_nodes = 1;

            pthread_mutex_unlock(&tree->root_mutex);
            pthread_rwlock_unlock(&tree->tree_lock);
            return true;
        }

        pthread_mutex_unlock(&tree->root_mutex);
        pthread_rwlock_unlock(&tree->tree_lock);

        // Retry with non-empty tree
        return bplus_tree_concurrent_insert(tree, key, value);
    }

    BPlusNode *current = tree->root;
    BPlusNode *locked_nodes[32];
    int num_locked = 0;

    // Lock root
    bplus_node_lock(current, LOCK_WRITE);
    locked_nodes[num_locked++] = current;
    pthread_mutex_unlock(&tree->root_mutex);

    // Traverse down using optimistic latch crabbing
    while (current->type == INTERNAL_NODE) {
        int index = bplus_node_find_key_index(current, key);
        if (index < current->num_keys && key >= current->keys[index]) {
            index++;
        }

        BPlusNode *child = current->children[index];
        if (!child) break;

        // Lock child
        bplus_node_lock(child, LOCK_WRITE);
        locked_nodes[num_locked++] = child;

        // If child is safe for insert, release all ancestors
        if (bplus_node_is_safe_for_insert(child)) {
            for (int i = 0; i < num_locked - 1; i++) {
                bplus_node_unlock(locked_nodes[i]);
            }
            locked_nodes[0] = child;
            num_locked = 1;
        }

        current = child;
    }

    // Now we have the leaf node locked
    BPlusNode *leaf = current;

    // Check if key already exists
    int index = bplus_node_find_key_index(leaf, key);
    if (index < leaf->num_keys && leaf->keys[index] == key) {
        leaf->values[index] = value;  // Update existing value
        bplus_release_locks(locked_nodes, num_locked);
        pthread_rwlock_unlock(&tree->tree_lock);
        return true;
    }

    // If leaf is not full, simple insert
    if (!bplus_node_is_full(leaf)) {
        bplus_node_insert_key_value(leaf, key, value);
        bplus_release_locks(locked_nodes, num_locked);
        pthread_rwlock_unlock(&tree->tree_lock);
        return true;
    }

    // Leaf is full, need to split - this requires structural changes
    bplus_release_locks(locked_nodes, num_locked);
    pthread_rwlock_unlock(&tree->tree_lock);

    // Upgrade to write lock for structural changes
    pthread_rwlock_wrlock(&tree->tree_lock);

    // Use the original non-concurrent insert for splits
    // In a production system, you'd implement concurrent splitting
    bool result = bplus_tree_insert(tree, key, value);

    pthread_rwlock_unlock(&tree->tree_lock);
    return result;
}

// Concurrent delete operation
bool bplus_tree_concurrent_delete(BPlusTree *tree, int key) {
    if (!tree) return false;

    BPlusNode *locked_nodes[32];
    int num_locked = 0;

    // Acquire tree read lock
    pthread_rwlock_rdlock(&tree->tree_lock);

    BPlusNode *leaf = bplus_tree_find_leaf_concurrent(tree, key, locked_nodes, &num_locked);

    if (!leaf) {
        pthread_rwlock_unlock(&tree->tree_lock);
        return false;
    }

    // Upgrade leaf lock to write
    bplus_node_unlock(leaf);
    bplus_node_lock(leaf, LOCK_WRITE);

    int index = bplus_node_find_key_index(leaf, key);
    if (index >= leaf->num_keys || leaf->keys[index] != key) {
        bplus_node_unlock(leaf);
        pthread_rwlock_unlock(&tree->tree_lock);
        return false;  // Key not found
    }

    // Simple delete (no underflow handling)
    bplus_node_shift_keys_left(leaf, index);
    leaf->num_keys--;

    bplus_node_unlock(leaf);
    pthread_rwlock_unlock(&tree->tree_lock);

    return true;
}

// Benchmark APIs
void *pth_bm_target_create() { return bplus_tree_create(); }
void pth_bm_target_destroy(void *target) { bplus_tree_destroy(target); }
void pth_bm_target_read(void *target, int key) { bplus_tree_concurrent_search(target, key, NULL); }
void pth_bm_target_insert(void *target, int key) { bplus_tree_concurrent_insert(target, key, 0xbeef); }
void pth_bm_target_update(void *target, int key) {}
void pth_bm_target_delete(void *target, int key) { bplus_tree_concurrent_delete(target, key); }
