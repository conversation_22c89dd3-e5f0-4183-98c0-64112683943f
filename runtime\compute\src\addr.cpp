#include "addr.h"
#include "init.hpp"

void* getLocalAddr(void* ptr) {
    // Implementation of getting local address
    // This function should return the local address associated with the given global address
    if (isLocalAddr(ptr)) {
        return ptr;
    }
    GlobalAddr gaddr = GlobalAddr::fromPointer(ptr);  // Cast the pointer to GlobalAddr
    void* local_addr = nullptr;
    global_state.data->cacheInsert(gaddr);
    local_addr = global_state.data->getLocalAddr(gaddr);
    if (local_addr == nullptr) {
        return ptr;
    }
    return local_addr;
}

bool isLocalAddr(void* ptr) {
    // Implementation of checking if the address is local
    // This function should return true if the address is local, false otherwise
    GlobalAddr gaddr = GlobalAddr::fromPointer(ptr);  // Cast the pointer to GlobalAddr
    return !gaddr.global_tag;
}

void addAddrDep(void* addr_u, void* addr_v) {
    // Implementation of adding address dependency
    global_state.data->addAddrDep(GlobalAddr::fromPointer(addr_u), GlobalAddr::from<PERSON>ointer(addr_v));
}

void acceptAddrDep(void* addr) {
    // Implementation of accepting address dependency
    global_state.data->acceptAddrDep(GlobalAddr::fromPointer(addr));
}
