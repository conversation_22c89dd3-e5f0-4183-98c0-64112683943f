#include "bplus_tree.h"

int main() {
    printf("Simple B+ Tree Test\n");
    printf("===================\n");

    // Create a small tree
    BPlusTree *tree = bplus_tree_create();
    if (!tree) {
        printf("Failed to create tree\n");
        return 1;
    }

    printf("Created tree with order 4\n");

    // Insert a few keys
    printf("Inserting keys: 10, 20, 5\n");
    bplus_tree_insert(tree, 10, 100);
    bplus_tree_insert(tree, 20, 200);
    bplus_tree_insert(tree, 5, 50);

    printf("Tree after insertions:\n");
    bplus_tree_print(tree);

    // Test searches
    printf("Testing searches:\n");
    int value;

    printf("Search 10: ");
    if (bplus_tree_search(tree, 10, &value)) {
        printf("Found! Value: %d\n", value);
    } else {
        printf("Not found\n");
    }

    printf("Search 20: ");
    if (bplus_tree_search(tree, 20, &value)) {
        printf("Found! Value: %d\n", value);
    } else {
        printf("Not found\n");
    }

    printf("Search 5: ");
    if (bplus_tree_search(tree, 5, &value)) {
        printf("Found! Value: %d\n", value);
    } else {
        printf("Not found\n");
    }

    printf("Search 15 (not inserted): ");
    if (bplus_tree_search(tree, 15, &value)) {
        printf("Found! Value: %d\n", value);
    } else {
        printf("Not found (correct)\n");
    }

    bplus_tree_destroy(tree);
    printf("Test completed successfully\n");

    return 0;
}
