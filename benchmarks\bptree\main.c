#include "bplus_tree.h"

void print_separator() { printf("================================================\n"); }

void test_basic_operations() {
    printf("Testing Basic B+ Tree Operations\n");
    print_separator();

    // Create a B+ tree with order 4
    BPlusTree *tree = bplus_tree_create(4);
    if (!tree) {
        printf("Failed to create tree\n");
        return;
    }

    printf("Created B+ tree with order 4\n\n");

    // Test insertions
    printf("Inserting keys: 10, 20, 5, 6, 12, 30, 7, 17\n");
    int keys[] = {10, 20, 5, 6, 12, 30, 7, 17};
    int values[] = {100, 200, 50, 60, 120, 300, 70, 170};
    int num_keys = sizeof(keys) / sizeof(keys[0]);

    for (int i = 0; i < num_keys; i++) {
        printf("Inserting (%d, %d)... ", keys[i], values[i]);
        if (bplus_tree_insert(tree, keys[i], values[i])) {
            printf("Success\n");
        } else {
            printf("Failed\n");
        }
    }

    printf("\nTree structure after insertions:\n");
    bplus_tree_print(tree);

    printf("Leaf traversal:\n");
    bplus_tree_traverse_leaves(tree);

    print_separator();

    // Test searches
    printf("Testing search operations:\n");
    int search_keys[] = {10, 5, 30, 15, 7};
    int num_search = sizeof(search_keys) / sizeof(search_keys[0]);

    for (int i = 0; i < num_search; i++) {
        int value;
        printf("Searching for key %d... ", search_keys[i]);
        if (bplus_tree_search(tree, search_keys[i], &value)) {
            printf("Found! Value: %d\n", value);
        } else {
            printf("Not found\n");
        }
    }

    print_separator();

    // Test deletions
    printf("Testing delete operations:\n");
    int delete_keys[] = {6, 30, 5};
    int num_delete = sizeof(delete_keys) / sizeof(delete_keys[0]);

    for (int i = 0; i < num_delete; i++) {
        printf("Deleting key %d... ", delete_keys[i]);
        if (bplus_tree_delete(tree, delete_keys[i])) {
            printf("Success\n");
        } else {
            printf("Failed (key not found)\n");
        }
    }

    printf("\nTree structure after deletions:\n");
    bplus_tree_print(tree);

    printf("Leaf traversal after deletions:\n");
    bplus_tree_traverse_leaves(tree);

    // Clean up
    bplus_tree_destroy(tree);
    printf("\nTree destroyed successfully\n");
}

void test_large_insertions() {
    printf("\nTesting Large Number of Insertions\n");
    print_separator();

    BPlusTree *tree = bplus_tree_create(5);
    if (!tree) {
        printf("Failed to create tree\n");
        return;
    }

    printf("Created B+ tree with order 5\n");
    printf("Inserting 20 sequential keys...\n");

    // Insert keys 1 through 20
    for (int i = 1; i <= 20; i++) {
        if (!bplus_tree_insert(tree, i, i * 10)) {
            printf("Failed to insert key %d\n", i);
        }
    }

    printf("\nTree structure after inserting 1-20:\n");
    bplus_tree_print(tree);

    printf("Leaf traversal:\n");
    bplus_tree_traverse_leaves(tree);

    // Test some searches
    printf("\nTesting searches on large tree:\n");
    int test_keys[] = {1, 10, 15, 20, 25};
    for (int i = 0; i < 5; i++) {
        int value;
        printf("Searching for key %d... ", test_keys[i]);
        if (bplus_tree_search(tree, test_keys[i], &value)) {
            printf("Found! Value: %d\n", value);
        } else {
            printf("Not found\n");
        }
    }

    bplus_tree_destroy(tree);
    printf("\nLarge tree test completed\n");
}

void interactive_mode() {
    printf("\nInteractive B+ Tree Mode\n");
    print_separator();

    BPlusTree *tree = bplus_tree_create(DEFAULT_ORDER);
    if (!tree) {
        printf("Failed to create tree\n");
        return;
    }

    printf("B+ Tree created with order %d\n", DEFAULT_ORDER);
    printf("Commands:\n");
    printf("  i <key> <value> - Insert key-value pair\n");
    printf("  s <key>         - Search for key\n");
    printf("  d <key>         - Delete key\n");
    printf("  p               - Print tree structure\n");
    printf("  t               - Traverse leaves\n");
    printf("  q               - Quit\n\n");

    char command;
    int key, value;

    while (1) {
        printf("Enter command: ");
        if (scanf(" %c", &command) != 1) {
            printf("Invalid input\n");
            continue;
        }

        switch (command) {
            case 'i':
                if (scanf("%d %d", &key, &value) == 2) {
                    if (bplus_tree_insert(tree, key, value)) {
                        printf("Inserted (%d, %d) successfully\n", key, value);
                    } else {
                        printf("Failed to insert (%d, %d)\n", key, value);
                    }
                } else {
                    printf("Usage: i <key> <value>\n");
                }
                break;

            case 's':
                if (scanf("%d", &key) == 1) {
                    if (bplus_tree_search(tree, key, &value)) {
                        printf("Key %d found with value %d\n", key, value);
                    } else {
                        printf("Key %d not found\n", key);
                    }
                } else {
                    printf("Usage: s <key>\n");
                }
                break;

            case 'd':
                if (scanf("%d", &key) == 1) {
                    if (bplus_tree_delete(tree, key)) {
                        printf("Key %d deleted successfully\n", key);
                    } else {
                        printf("Key %d not found or failed to delete\n", key);
                    }
                } else {
                    printf("Usage: d <key>\n");
                }
                break;

            case 'p':
                bplus_tree_print(tree);
                break;

            case 't':
                bplus_tree_traverse_leaves(tree);
                break;

            case 'q':
                printf("Exiting interactive mode\n");
                bplus_tree_destroy(tree);
                return;

            default:
                printf("Unknown command: %c\n", command);
                break;
        }
    }
}

int main() {
    printf("B+ Tree Implementation in C\n");
    printf("===========================\n\n");

    // Run basic tests
    test_basic_operations();

    // Run large insertion test
    test_large_insertions();

    // Ask user if they want interactive mode
    printf("\nWould you like to try interactive mode? (y/n): ");
    char choice;
    if (scanf(" %c", &choice) == 1 && (choice == 'y' || choice == 'Y')) {
        interactive_mode();
    }

    printf("\nProgram completed successfully!\n");
    return 0;
}
