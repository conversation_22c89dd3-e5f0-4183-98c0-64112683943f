#ifndef LRU_GLOBAL_ADDR_H
#define LRU_GLOBAL_ADDR_H

#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// Global address structure for LRU cache manager
// Compatible with compute/include/addr.h but optimized for LRU operations
union GlobalAddr {
    struct {
        uint8_t typeID : 8;     // Type identifier for different data types
        uint64_t offset : 56;   // Offset within the memory space
    };
    uint64_t val;               // Raw 64-bit value for fast operations

#ifdef __cplusplus
    static GlobalAddr null() { return {.val = 0}; }
    bool operator==(const GlobalAddr& other) const { return val == other.val; }
    bool operator!=(const GlobalAddr& other) const { return val != other.val; }
    bool operator<(const GlobalAddr& other) const { return val < other.val; }
    
    // Transform to a pointer type
    operator void*() const {
        return reinterpret_cast<void*>(val);
    }
    
    // Transform from a pointer type
    static GlobalAddr fromPointer(void* ptr) {
        GlobalAddr gaddr;
        gaddr.val = reinterpret_cast<uint64_t>(ptr);
        return gaddr;
    }
#endif
};

// Core address management functions
void* getLocalAddr(void* gaddr);
bool isLocalAddr(void* gaddr);

// Address dependency management for cache coherence
void addAddrDep(void* addr_u, void* addr_v);
void acceptAddrDep(void* addr);

// LRU-specific address operations
bool lru_cache_contains(void* gaddr);
void lru_cache_touch(void* gaddr);           // Mark as recently used
void lru_cache_invalidate(void* gaddr);      // Remove from cache
void lru_cache_flush(void* gaddr);           // Write back if dirty
void lru_cache_prefetch(void* gaddr, size_t size);  // Prefetch into cache

// Cache statistics and monitoring
size_t lru_cache_size(void);                 // Current cache size in bytes
size_t lru_cache_entry_count(void);          // Number of cached entries
double lru_cache_hit_rate(void);             // Cache hit rate (0.0 to 1.0)
void lru_cache_print_stats(void);            // Print detailed statistics

// Cache management
void lru_cache_clear(void);                  // Clear all cache entries
void lru_cache_set_max_size(size_t max_size); // Set maximum cache size
size_t lru_cache_get_max_size(void);         // Get maximum cache size

// Advanced cache operations
void lru_cache_mark_dirty(void* gaddr);      // Mark entry as dirty
void lru_cache_flush_all(void);              // Flush all dirty entries
void lru_cache_evict_lru(void);              // Manually evict LRU entry

#ifdef __cplusplus
}

#include <unordered_map>
#include <functional>

// C++ hash and equality functions for GlobalAddr
namespace std {
template <>
struct hash<GlobalAddr> {
    std::size_t operator()(const GlobalAddr& g) const noexcept {
        return std::hash<uint64_t>()(g.val);
    }
};

template <>
struct equal_to<GlobalAddr> {
    bool operator()(const GlobalAddr& lhs, const GlobalAddr& rhs) const noexcept {
        return lhs.val == rhs.val;
    }
};
}  // namespace std
#endif

#endif  // LRU_GLOBAL_ADDR_H
