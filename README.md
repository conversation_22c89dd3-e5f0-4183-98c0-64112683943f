# Pentathlon
```
cmake .. -G Ninja \
  -DMLIR_DIR=~/Polygeist/llvm-project/build/lib/cmake/mlir \
  -DLLVM_DIR=~/Polygeist/llvm-project/build/lib/cmake/llvm
```

```
void addAddrDep(size_t addr_u, size_t addr_v);
void acceptAddrDep(size_t addr);
size_t getLocalAddr(size_t addr);
size_t disaggAlloc(size_t size);
void disaggFree(size_t addr);
```

hash table (v.addr+offset, (u.addr, v.addr, 0/1))

==============================

LRU cache

1. hash table of address translation
2. RDMA + prefetching
3. LRU eviction

===============================

Our cache

1. hash table of address translation
2. RDMA + prefetching
3. hash table of acc dep
4. prefetching

===============================

YCSB

B+-tree

hash table

skip list

===============================

Mira

1. RDMA + prefetching
2. Optimization
3. Caching

===============================

<!-- ~/Polygeist/build/bin/cgeist test.c -function=* -S --raise-scf-to-affine --polyhedral-opt | ~/Polygeist/build/bin/polygeist-opt --canonicalize --cse > output.mlir
~/Polygeist/llvm-project/build/bin/mlir-opt --lower-affine --convert-scf-to-cf --convert-arith-to-llvm --convert-cf-to-llvm --convert-to-llvm --reconcile-unrealized-casts output.mlir -o test.mlir
~/Polygeist/llvm-project/build/bin/mlir-translate --mlir-to-llvmir test.mlir -o test.ll -->

~/Polygeist/build/bin/cgeist test.c -function=* -S -emit-llvm-dialect