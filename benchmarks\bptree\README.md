# B+ Tree Implementation in C

A complete implementation of a B+ tree data structure in C, featuring insertion, search, deletion, and traversal operations.

## Features

- **Complete B+ Tree Implementation**: Supports all core operations
- **Thread-Safe Concurrent Operations**: Latch crabbing technique for maximum concurrency
- **Configurable Order**: Tree order can be specified during creation
- **Memory Safe**: Proper memory management with no leaks
- **Interactive Mode**: Command-line interface for testing
- **Comprehensive Testing**: Multiple test scenarios including concurrent stress tests

## Files

- `bplus_tree.h` - Header file with structure definitions and function declarations
- `bplus_tree.c` - Core B+ tree implementation
- `main.c` - Demo program with comprehensive tests and interactive mode
- `test_simple.c` - Simple test program for basic functionality
- `test_split.c` - Test program specifically for node splitting
- `test_concurrent.c` - Comprehensive concurrent operations stress test
- `Makefile` - Build configuration with pthread support

## B+ Tree Properties

This implementation maintains all standard B+ tree properties:

1. **All data stored in leaf nodes**: Internal nodes only contain keys for navigation
2. **Leaf nodes linked**: All leaf nodes are connected for efficient range queries
3. **Balanced structure**: All leaf nodes are at the same level
4. **Order constraints**: Each node (except root) contains at least ⌈order/2⌉ - 1 keys
5. **Sorted keys**: Keys are maintained in sorted order within each node

## Core Operations

### Sequential Operations
- `bool bplus_tree_insert(BPlusTree *tree, int key, int value)`
- `bool bplus_tree_search(BPlusTree *tree, int key, int *value)`
- `bool bplus_tree_delete(BPlusTree *tree, int key)`

### Concurrent Operations (NEW!)
- `bool bplus_tree_concurrent_insert(BPlusTree *tree, int key, int value)`
- `bool bplus_tree_concurrent_search(BPlusTree *tree, int key, int *value)`
- `bool bplus_tree_concurrent_delete(BPlusTree *tree, int key)`

#### Insert
- Inserts a key-value pair into the tree
- Handles node splits automatically when nodes become full
- Maintains tree balance and properties
- **Concurrent version uses latch crabbing for thread safety**

#### Search
- Searches for a key and returns its associated value
- Efficient O(log n) search time
- Returns true if found, false otherwise
- **Concurrent version allows multiple readers simultaneously**

#### Delete
- Removes a key-value pair from the tree
- Note: Current implementation is simplified and doesn't handle underflow
- **Concurrent version uses proper locking**

### Traversal
- `void bplus_tree_traverse_leaves(BPlusTree *tree)`
- Traverses all leaf nodes in order
- Demonstrates the linked nature of leaf nodes

## Concurrent Implementation

### Latch Crabbing Technique

The concurrent B+ tree implementation uses the **latch crabbing** (also known as lock coupling) technique to ensure thread safety while maximizing concurrency:

#### Key Concepts:
1. **Read-Write Locks**: Each node has a pthread_rwlock_t for fine-grained locking
2. **Tree-Level Lock**: Global rwlock for structural changes (splits, root changes)
3. **Lock Coupling**: Acquire child lock before releasing parent lock during traversal
4. **Optimistic Locking**: Release ancestor locks early when safe nodes are found

#### Latch Crabbing Protocol:

**For Search Operations:**
1. Acquire read lock on root
2. Traverse down the tree, acquiring read locks on children
3. Release parent lock after acquiring child lock (crabbing)
4. Continue until reaching target leaf node
5. Perform search operation
6. Release leaf lock

**For Insert Operations:**
1. Acquire write lock on root
2. Traverse down, acquiring write locks on children
3. If child is "safe" (won't split), release all ancestor locks
4. Continue to leaf and perform insertion
5. If split is needed, fall back to global tree lock (current limitation)

**For Delete Operations:**
1. Similar to insert but checks for "safe" nodes (won't underflow)
2. Simplified implementation without underflow handling

#### Thread Safety Features:
- **Multiple Readers**: Read operations can proceed concurrently
- **Reader-Writer Exclusion**: Writes block reads and other writes on same node
- **Deadlock Prevention**: Consistent lock ordering (parent before child)
- **Memory Safety**: Proper cleanup with deleted node marking

## Building and Running

### Compile the main program:
```bash
make
```

### Run the demo:
```bash
./bplus_tree
```

### Compile and run simple test:
```bash
gcc -Wall -Wextra -g -std=c99 -o test_simple test_simple.c bplus_tree.c
./test_simple
```

### Test concurrent operations:
```bash
make test-concurrent
```

### Run with memory checking:
```bash
valgrind --tool=memcheck --leak-check=full ./bplus_tree
```

### Run with race condition detection:
```bash
make valgrind-concurrent
```

### Clean build files:
```bash
make clean
```

## Usage Examples

### Sequential Operations
```c
#include "bplus_tree.h"

int main() {
    // Create a B+ tree with order 4
    BPlusTree *tree = bplus_tree_create(4);

    // Insert some key-value pairs
    bplus_tree_insert(tree, 10, 100);
    bplus_tree_insert(tree, 20, 200);
    bplus_tree_insert(tree, 5, 50);

    // Search for a key
    int value;
    if (bplus_tree_search(tree, 10, &value)) {
        printf("Found key 10 with value %d\n", value);
    }

    // Print tree structure
    bplus_tree_print(tree);

    // Traverse all leaves
    bplus_tree_traverse_leaves(tree);

    // Clean up
    bplus_tree_destroy(tree);

    return 0;
}
```

### Concurrent Operations
```c
#include "bplus_tree.h"
#include <pthread.h>

void* worker_thread(void* arg) {
    BPlusTree *tree = (BPlusTree*)arg;

    // Concurrent insertions
    for (int i = 0; i < 1000; i++) {
        bplus_tree_concurrent_insert(tree, i, i * 10);
    }

    // Concurrent searches
    for (int i = 0; i < 1000; i++) {
        int value;
        bplus_tree_concurrent_search(tree, i, &value);
    }

    return NULL;
}

int main() {
    BPlusTree *tree = bplus_tree_create(4);

    // Create multiple threads
    pthread_t threads[4];
    for (int i = 0; i < 4; i++) {
        pthread_create(&threads[i], NULL, worker_thread, tree);
    }

    // Wait for completion
    for (int i = 0; i < 4; i++) {
        pthread_join(threads[i], NULL);
    }

    bplus_tree_destroy(tree);
    return 0;
}
```

## Interactive Mode

The main program includes an interactive mode where you can:

- `i <key> <value>` - Insert a key-value pair
- `s <key>` - Search for a key
- `d <key>` - Delete a key
- `p` - Print the tree structure
- `t` - Traverse all leaf nodes
- `q` - Quit

## Test Results

The implementation successfully:

1. **Basic Operations**: Inserts, searches, and deletes work correctly
2. **Node Splitting**: Properly handles leaf and internal node splits
3. **Tree Balance**: Maintains balanced tree structure
4. **Memory Management**: No memory leaks or corruption (verified with Valgrind)
5. **Large Datasets**: Handles insertion of 20+ sequential keys with proper tree growth
6. **Concurrent Operations**: Thread-safe operations using latch crabbing
7. **High Concurrency**: Multiple readers can access simultaneously
8. **Performance**: Achieves high throughput in concurrent scenarios

## Implementation Details

### Node Structure
- **Internal Nodes**: Store keys and child pointers for navigation
- **Leaf Nodes**: Store key-value pairs and link to next leaf
- **Memory Layout**: Efficient memory allocation with overflow protection

### Split Algorithm
- **Leaf Split**: Splits at middle, promotes first key of new leaf
- **Internal Split**: Splits at middle, promotes middle key to parent
- **Root Split**: Creates new root when original root splits

### Search Algorithm
- **Top-down traversal**: Starts from root and follows appropriate child pointers
- **Leaf search**: Linear search within the target leaf node
- **Efficient navigation**: Uses binary search concept for child selection

## Limitations

1. **Delete Operation**: Current delete implementation is simplified and doesn't handle:
   - Node underflow
   - Merging of nodes
   - Key redistribution

2. **Concurrent Splits**: Complex split operations fall back to global locking
   - Future enhancement: Implement fully concurrent splitting

3. **Data Types**: Currently supports only integer keys and values
4. **Persistence**: In-memory only, no disk storage

## Future Enhancements

- Complete delete operation with underflow handling
- Fully concurrent split operations without global locking
- Generic data type support using void pointers
- Disk-based storage for persistence
- Range query operations with concurrent support
- Bulk loading optimization
- Lock-free operations using advanced techniques
- NUMA-aware memory allocation for large systems

## License

This implementation is provided as educational material and can be freely used and modified.
