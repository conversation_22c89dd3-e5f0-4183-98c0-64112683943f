#!/bin/bash

# Compilation script for skiplist using MLIR/LLVM toolchain
# Pipeline:
# 1. Use cgeist to compile skiplist to LLVM Dialect
# 2. Use my-opt to transform LLVM Dialect
# 3. Use mlir-translate to transform LLVM Dialect to LLVM IR
# 4. Use llc to transform LLVM IR to assembly

set -e  # Exit on any error

# Configuration
POLYGEIST_PATH="$HOME/Polygeist/build"
LLVM_PROJECT_PATH="$HOME/Polygeist/llvm-project/build"
COMPILER_BUILD_PATH="../../compiler/build"  # Relative to this script location
OUTPUT_DIR="./output"
SKIPLIST_DIR="../skiplist"

# Tool paths
CGEIST="$POLYGEIST_PATH/bin/cgeist"
MY_OPT="$COMPILER_BUILD_PATH/bin/my-opt"
MLIR_TRANSLATE="$LLVM_PROJECT_PATH/bin/mlir-translate"
LLC="$LLVM_PROJECT_PATH/bin/llc"

# Flags
CGEIST_FLAGS="-emit-llvm-dialect -S -function=*"
# 4 passes
MY_OPT_FLAGS="--addr-dep-pass --disagg-alloc-pass --disagg-free-pass --local-addr-pass"
LLC_FLAGS=""

# Source files
SKIPLIST_SOURCES="$SKIPLIST_DIR/skip_list.cxx"

# Include additional source files if needed
# SKIPLIST_SOURCES="$SKIPLIST_SOURCES $SKIPLIST_DIR/other_file.cxx"

# Output files
LLVM_DIALECT_OUTPUT="$OUTPUT_DIR/skiplist_llvm_dialect.mlir"
LLVM_DIALECT_FIXED="$OUTPUT_DIR/skiplist_llvm_dialect_fixed.mlir"
OPTIMIZED_OUTPUT="$OUTPUT_DIR/skiplist_optimized.mlir"
LLVM_IR_OUTPUT="$OUTPUT_DIR/skiplist.ll"
ASSEMBLY_OUTPUT="$OUTPUT_DIR/skiplist.s"

# Create output directory
mkdir -p "$OUTPUT_DIR"

echo "=== Skiplist Compilation Pipeline ==="
echo "Source: $SKIPLIST_SOURCES"
echo "Output directory: $OUTPUT_DIR"
echo

# Check if tools exist
echo "Checking tool availability..."
for tool in "$CGEIST" "$MY_OPT" "$MLIR_TRANSLATE" "$LLC"; do
    if [[ ! -x "$tool" ]]; then
        echo "Error: Tool not found or not executable: $tool"
        exit 1
    fi
done
echo "All tools found."
echo

# Step 1: Use cgeist to compile skiplist to LLVM Dialect
echo "Step 1: Compiling C++ source to LLVM Dialect using cgeist..."
echo "Command: $CGEIST $CGEIST_FLAGS $SKIPLIST_SOURCES > $LLVM_DIALECT_OUTPUT"
if ! "$CGEIST" $CGEIST_FLAGS "$SKIPLIST_SOURCES" > "$LLVM_DIALECT_OUTPUT"; then
    echo "Error: cgeist compilation failed"
    exit 1
fi
echo "✓ LLVM Dialect generated: $LLVM_DIALECT_OUTPUT"
echo

# Step 1.5: Fix LLVM Dialect (replace [] with [0] in llvm.getelement)
echo "Step 1.5: Fixing LLVM Dialect patterns..."
SCRIPT_DIR="$(dirname "$0")"
if [[ -x "$SCRIPT_DIR/fix_llvm_dialect.sh" ]]; then
    echo "Running LLVM dialect fixer..."
    "$SCRIPT_DIR/fix_llvm_dialect.sh" -i "$LLVM_DIALECT_OUTPUT" -o "$LLVM_DIALECT_FIXED" -v
    echo "✓ LLVM Dialect fixed: $LLVM_DIALECT_FIXED"
    # Use the fixed file for subsequent steps
    LLVM_DIALECT_FOR_OPT="$LLVM_DIALECT_FIXED"
else
    echo "Warning: fix_llvm_dialect.sh not found or not executable, using original file"
    LLVM_DIALECT_FOR_OPT="$LLVM_DIALECT_OUTPUT"
fi
echo

# Step 2: Use my-opt to transform LLVM Dialect
echo "Step 2: Optimizing LLVM Dialect using my-opt..."
echo "Command: $MY_OPT $MY_OPT_FLAGS $LLVM_DIALECT_FOR_OPT -o $OPTIMIZED_OUTPUT"
if ! "$MY_OPT" $MY_OPT_FLAGS "$LLVM_DIALECT_FOR_OPT" -o "$OPTIMIZED_OUTPUT"; then
    echo "Error: my-opt optimization failed"
    exit 1
fi
echo "✓ Optimized LLVM Dialect generated: $OPTIMIZED_OUTPUT"
echo

# Step 3: Use mlir-translate to transform LLVM Dialect to LLVM IR
echo "Step 3: Translating LLVM Dialect to LLVM IR using mlir-translate..."
echo "Command: $MLIR_TRANSLATE --mlir-to-llvmir $OPTIMIZED_OUTPUT -o $LLVM_IR_OUTPUT"
if ! "$MLIR_TRANSLATE" --mlir-to-llvmir "$OPTIMIZED_OUTPUT" -o "$LLVM_IR_OUTPUT"; then
    echo "Error: mlir-translate failed"
    exit 1
fi
echo "✓ LLVM IR generated: $LLVM_IR_OUTPUT"
echo

# Step 4: Use llc to transform LLVM IR to assembly
echo "Step 4: Compiling LLVM IR to assembly using llc..."
echo "Command: $LLC $LLC_FLAGS $LLVM_IR_OUTPUT -o $ASSEMBLY_OUTPUT"
if ! "$LLC" $LLC_FLAGS "$LLVM_IR_OUTPUT" -o "$ASSEMBLY_OUTPUT"; then
    echo "Error: llc compilation failed"
    exit 1
fi
echo "✓ Assembly generated: $ASSEMBLY_OUTPUT"
echo

echo "=== Compilation Pipeline Complete ==="
echo "Generated files:"
echo "  - LLVM Dialect (original): $LLVM_DIALECT_OUTPUT"
if [[ -f "$LLVM_DIALECT_FIXED" ]]; then
    echo "  - LLVM Dialect (fixed): $LLVM_DIALECT_FIXED"
fi
echo "  - Optimized LLVM Dialect: $OPTIMIZED_OUTPUT"
echo "  - LLVM IR: $LLVM_IR_OUTPUT"
echo "  - Assembly: $ASSEMBLY_OUTPUT"
echo

# Optional: Show file sizes
echo "File sizes:"
FILES_TO_CHECK=("$LLVM_DIALECT_OUTPUT" "$OPTIMIZED_OUTPUT" "$LLVM_IR_OUTPUT" "$ASSEMBLY_OUTPUT")
if [[ -f "$LLVM_DIALECT_FIXED" ]]; then
    FILES_TO_CHECK+=("$LLVM_DIALECT_FIXED")
fi
for file in "${FILES_TO_CHECK[@]}"; do
    if [[ -f "$file" ]]; then
        size=$(wc -c < "$file")
        echo "  $(basename "$file"): $size bytes"
    fi
done

