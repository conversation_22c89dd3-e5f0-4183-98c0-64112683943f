#!/bin/bash

# Build script for B+ tree - supports both regular and optimized compilation
# Usage: ./build_bptree.sh [regular|optimized|both|clean]

set -e  # Exit on any error

SCRIPT_DIR="$(dirname "$0")"
OUTPUT_DIR="$SCRIPT_DIR/output"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  B+ Tree Build Script${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

clean_output() {
    print_info "Cleaning output directory..."
    if [[ -d "$OUTPUT_DIR" ]]; then
        rm -rf "$OUTPUT_DIR"
        print_success "Output directory cleaned"
    else
        print_info "Output directory doesn't exist, nothing to clean"
    fi
}

build_regular() {
    print_info "Building regular B+ tree..."
    if [[ -x "$SCRIPT_DIR/compile_bptree.sh" ]]; then
        cd "$SCRIPT_DIR"
        ./compile_bptree.sh
        print_success "Regular B+ tree compilation completed"
    else
        print_error "compile_bptree.sh not found or not executable"
        return 1
    fi
}

build_optimized() {
    print_info "Building optimized B+ tree..."
    if [[ -x "$SCRIPT_DIR/compile_bptree_optimized.sh" ]]; then
        cd "$SCRIPT_DIR"
        ./compile_bptree_optimized.sh
        print_success "Optimized B+ tree compilation completed"
    else
        print_error "compile_bptree_optimized.sh not found or not executable"
        return 1
    fi
}

show_results() {
    print_info "Build results:"
    if [[ -d "$OUTPUT_DIR" ]]; then
        echo "Generated files in $OUTPUT_DIR:"
        ls -la "$OUTPUT_DIR" | grep -E '\.(mlir|ll|s|o|a)$' | while read -r line; do
            echo "  $line"
        done
        
        echo
        print_info "File sizes summary:"
        find "$OUTPUT_DIR" -name "*.mlir" -o -name "*.ll" -o -name "*.s" -o -name "*.o" -o -name "*.a" | while read -r file; do
            if [[ -f "$file" ]]; then
                size=$(wc -c < "$file")
                printf "  %-30s: %8d bytes\n" "$(basename "$file")" "$size"
            fi
        done
    else
        print_warning "No output directory found"
    fi
}

show_usage() {
    echo "Usage: $0 [regular|optimized|both|clean|help]"
    echo
    echo "Commands:"
    echo "  regular    - Build regular B+ tree version"
    echo "  optimized  - Build optimized B+ tree version"
    echo "  both       - Build both regular and optimized versions"
    echo "  clean      - Clean output directory"
    echo "  help       - Show this help message"
    echo
    echo "Examples:"
    echo "  $0 regular     # Build regular version only"
    echo "  $0 optimized   # Build optimized version only"
    echo "  $0 both        # Build both versions"
    echo "  $0 clean       # Clean all generated files"
}

# Main script logic
print_header

case "${1:-both}" in
    "regular")
        build_regular
        show_results
        ;;
    "optimized")
        build_optimized
        show_results
        ;;
    "both")
        print_info "Building both regular and optimized versions..."
        build_regular
        echo
        build_optimized
        show_results
        ;;
    "clean")
        clean_output
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        echo
        show_usage
        exit 1
        ;;
esac

echo
print_info "Build script completed"

# Make the compilation scripts executable if they aren't already
chmod +x "$SCRIPT_DIR/compile_bptree.sh" 2>/dev/null || true
chmod +x "$SCRIPT_DIR/compile_bptree_optimized.sh" 2>/dev/null || true
