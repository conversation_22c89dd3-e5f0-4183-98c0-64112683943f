# Thread Safety Fixes for runtime/compute

## Overview
This document summarizes the multithreading issues found in the runtime/compute directory and the fixes implemented to resolve them.

## Issues Identified

### 1. RDMA Client Thread Safety Issues
**Problem**: The `RDMAClient` class used a shared buffer (`buf`) without synchronization, causing data corruption when multiple threads called `read()` or `write()` simultaneously.

**Solution**: 
- Implemented per-thread buffers using `ThreadBuffer` struct
- Added thread-safe buffer management with `std::unordered_map<std::thread::id, std::unique_ptr<ThreadBuffer>>`
- Protected buffer map access with mutex
- Each thread gets its own registered memory region for RDMA operations

### 2. DataManager Deadlock Risks
**Problem**: Multiple mutex acquisitions without consistent ordering, manual lock/unlock patterns, and race conditions in cache operations.

**Solution**:
- Replaced all manual lock/unlock with RAII `std::lock_guard`
- Implemented consistent lock ordering (always lock lower address first in dependency operations)
- Separated critical sections to minimize lock holding time
- Fixed scope issues with lock guards

### 3. Cache Size Race Condition
**Problem**: `cache_size` was not atomic but accessed from multiple threads.

**Solution**: Changed `cache_size` from `size_t` to `std::atomic<size_t>` for thread-safe access.

### 4. Inconsistent Locking Patterns
**Problem**: Mix of manual lock/unlock and RAII lock guards throughout the codebase.

**Solution**: Standardized on RAII `std::lock_guard` for all mutex operations.

## Detailed Changes

### rdma.hpp
- Added thread-local buffer support with `ThreadBuffer` struct
- Added per-thread buffer management to `RDMAClient` class
- Added `get_thread_buffer()` method for thread-safe buffer access

### rdma.cpp
- Implemented `get_thread_buffer()` method
- Updated `read()` and `write()` methods to use thread-local buffers
- Removed shared buffer usage

### data-manager.hpp
- Changed `cache_size` to `std::atomic<size_t>`
- Fixed `get_size()`, `get_offset()`, and `get_type_id()` to use RAII locking
- Updated `update_cache_leaf_remove()` and `update_cache_leaf_insert()` for thread safety

### data-manager.cpp
- **disaggFree()**: Fixed deadlock potential by proper lock scoping and RAII usage
- **disaggAlloc()**: Added proper synchronization for chunk allocation
- **addAddrDep()**: Converted to RAII locking
- **acceptAddrDep()**: Complete rewrite to prevent deadlocks with consistent lock ordering
- **getLocalAddr()**: Already used RAII, no changes needed
- **cacheInsert()**: Major refactoring to prevent race conditions and deadlocks in cache eviction

## Key Improvements

### 1. Deadlock Prevention
- Consistent lock ordering: always lock nodes in address order (lower address first)
- Separated critical sections to minimize lock holding time
- Eliminated nested lock acquisitions where possible

### 2. Race Condition Elimination
- Made cache_size atomic
- Protected all shared data structure access with appropriate mutexes
- Used RAII for automatic lock management

### 3. Exception Safety
- RAII ensures locks are released even if exceptions occur
- Proper resource cleanup in destructors

### 4. Performance Improvements
- Per-thread RDMA buffers eliminate contention
- Reduced lock holding time by separating operations
- Atomic operations for simple counters

## Testing
Created comprehensive thread safety tests in `tests/thread_safety_test.cpp`:
- Concurrent allocation/deallocation tests
- Concurrent cache operation tests
- Concurrent dependency operation tests
- Concurrent RDMA operation tests

## Verification
To verify the fixes:
1. Compile the runtime/compute library
2. Run the thread safety tests
3. Use thread sanitizer tools (TSan) for additional verification
4. Stress test with high concurrency workloads

## Future Considerations
1. Consider using lock-free data structures for high-performance scenarios
2. Implement more granular locking if performance bottlenecks are identified
3. Add performance monitoring for lock contention
4. Consider using reader-writer locks for read-heavy workloads

## Notes
- All changes maintain backward compatibility with existing APIs
- Memory usage slightly increased due to per-thread buffers
- Performance should improve due to reduced lock contention
- Code is now much safer for multithreaded environments
