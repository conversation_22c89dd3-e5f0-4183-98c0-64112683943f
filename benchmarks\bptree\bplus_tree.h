#ifndef BPLUS_TREE_H
#define BPLUS_TREE_H

#ifndef _GNU_SOURCE
#define _GNU_SOURCE
#endif

#include <pthread.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

// Default order of the B+ tree (can be changed)
// Should derive from actual page size, so that the resulting `BPlusNode`'s size is close to size of
// one page.
#define DEFAULT_ORDER 332

// Lock modes for latch crabbing
typedef enum { LOCK_NONE, LOCK_READ, LOCK_WRITE } LockMode;

// Forward declarations
typedef struct BPlusNode BPlusNode;
typedef struct BPlusTree BPlusTree;

// Node types
typedef enum { INTERNAL_NODE, LEAF_NODE } NodeType;

// B+ Tree Node structure
struct BPlusNode {
    NodeType type;            // Type of node (internal or leaf)
    int num_keys;             // Current number of keys in the node
    int keys[DEFAULT_ORDER];  // Array of keys (extra space for temporary overflow during splits)

    // For internal nodes
    struct BPlusNode *children[DEFAULT_ORDER + 1];  // Array of child pointers (extra space for
                                                    // temporary overflow during splits)

    // For leaf nodes
    int *values;             // Array of values (data)
    struct BPlusNode *next;  // Pointer to next leaf node (for range queries)

    // Parent pointer for easier navigation
    struct BPlusNode *parent;

    // Concurrency control
    pthread_rwlock_t rwlock;   // Read-write lock for this node
    volatile bool is_deleted;  // Flag to mark node as deleted (for safe memory management)
};

// B+ Tree structure
struct BPlusTree {
    BPlusNode *root;  // Root node of the tree
    int height;       // Height of the tree
    int num_nodes;    // Total number of nodes

    // Concurrency control
    pthread_rwlock_t tree_lock;  // Global tree lock for structural changes
    pthread_mutex_t root_mutex;  // Mutex for root pointer changes
};

// Function declarations

// Tree management
BPlusTree *bplus_tree_create();
void bplus_tree_destroy(BPlusTree *tree);
void bplus_tree_print(BPlusTree *tree);

// Node management
BPlusNode *bplus_node_create(NodeType type);
void bplus_node_destroy(BPlusNode *node);
bool bplus_node_is_full(BPlusNode *node);
bool bplus_node_is_minimal(BPlusNode *node);

// Core operations
bool bplus_tree_insert(BPlusTree *tree, int key, int value);
bool bplus_tree_search(BPlusTree *tree, int key, int *value);
bool bplus_tree_delete(BPlusTree *tree, int key);

// Concurrent operations
bool bplus_tree_concurrent_insert(BPlusTree *tree, int key, int value);
bool bplus_tree_concurrent_search(BPlusTree *tree, int key, int *value);
bool bplus_tree_concurrent_delete(BPlusTree *tree, int key);

// Helper functions
BPlusNode *bplus_tree_find_leaf(BPlusTree *tree, int key);
void bplus_node_insert_key_value(BPlusNode *leaf, int key, int value);
void bplus_node_insert_key_child(BPlusNode *internal, int key, BPlusNode *child);
BPlusNode *bplus_node_split_leaf(BPlusNode *leaf);
BPlusNode *bplus_node_split_internal(BPlusNode *internal);
void bplus_tree_insert_into_parent(BPlusTree *tree, BPlusNode *left, int key, BPlusNode *right);

// Traversal and display
void bplus_tree_traverse_leaves(BPlusTree *tree);
void bplus_tree_print_tree(BPlusNode *node, int level);

// Utility functions
int bplus_node_find_key_index(BPlusNode *node, int key);
void bplus_node_shift_keys_right(BPlusNode *node, int start_index);
void bplus_node_shift_keys_left(BPlusNode *node, int start_index);

// Latch crabbing functions
void bplus_node_lock(BPlusNode *node, LockMode mode);
void bplus_node_unlock(BPlusNode *node);
bool bplus_node_try_lock(BPlusNode *node, LockMode mode);
BPlusNode *bplus_tree_find_leaf_concurrent(BPlusTree *tree, int key, BPlusNode **locked_nodes,
                                           int *num_locked);
void bplus_release_locks(BPlusNode **locked_nodes, int num_locked);
bool bplus_node_is_safe_for_insert(BPlusNode *node);
bool bplus_node_is_safe_for_delete(BPlusNode *node);

#endif  // BPLUS_TREE_H
