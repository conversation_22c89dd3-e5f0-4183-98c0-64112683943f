#include "rdma.hpp"
#include <infiniband/verbs.h>
#include <cstdint>
#include <cstring>
#include <stdexcept>
#include "addr.h"

// Thread-safe RDMA implementation using per-thread buffers

ThreadBuffer* RDMAClient::get_thread_buffer() {
    std::thread::id tid = std::this_thread::get_id();

    std::lock_guard<std::mutex> lock(buffer_map_mutex);
    auto it = thread_buffers.find(tid);
    if (it != thread_buffers.end()) {
        return it->second.get();
    }

    // Create new thread buffer
    auto buffer = std::make_unique<ThreadBuffer>(inner->conn->pd);
    ThreadBuffer* buffer_ptr = buffer.get();
    thread_buffers[tid] = std::move(buffer);
    return buffer_ptr;
}

void RDMAClient::read(GlobalAddr from, void* to, uint32_t size) {
    if (size > CACHE_LINE_SIZE)
        throw std::runtime_error("read must be smaller than or equal to cacheline size");

    ThreadBuffer* thread_buf = get_thread_buffer();

    ibv_sge sg = {.addr = (uint64_t)thread_buf->buf, .length = size, .lkey = thread_buf->buf_mr->lkey};
    ibv_send_wr wr = {
        .sg_list = &sg,
        .num_sge = 1,
        .opcode = IBV_WR_RDMA_READ,
        .send_flags = IBV_SEND_SIGNALED,
        .wr = {.rdma = {.remote_addr = from.offset, .rkey = inner->mem.rkey}},
    };

    ibv_send_wr* bad = nullptr;
    if (ibv_post_send(inner->conn->id->qp, &wr, &bad) < 0)
        throw std::runtime_error("failed to post send");
    if (bad) throw std::runtime_error("bad wr pointer non null");

    ibv_wc wc;
    while (true) {
        int ret = ibv_poll_cq(inner->conn->send_cq, 1, &wc);
        if (ret == 1) break;
        if (ret < 0) throw std::runtime_error("failed to poll completion queue");
    }
    if (wc.status != IBV_WC_SUCCESS) throw std::runtime_error("work completion error");

    memcpy(to, thread_buf->buf, size);
}

void RDMAClient::write(void* from, GlobalAddr to, uint32_t size) {
    if (size > CACHE_LINE_SIZE)
        throw std::runtime_error("write must be smaller than or equal to cacheline size");

    ThreadBuffer* thread_buf = get_thread_buffer();
    memcpy(thread_buf->buf, from, size);

    ibv_sge sg = {.addr = (uint64_t)thread_buf->buf, .length = size, .lkey = thread_buf->buf_mr->lkey};
    ibv_send_wr wr = {
        .sg_list = &sg,
        .num_sge = 1,
        .opcode = IBV_WR_RDMA_WRITE,
        .send_flags = IBV_SEND_SIGNALED,
        .wr = {.rdma = {.remote_addr = to.offset, .rkey = inner->mem.rkey}},
    };

    ibv_send_wr* bad = nullptr;
    if (ibv_post_send(inner->conn->id->qp, &wr, &bad) < 0)
        throw std::runtime_error("failed to post send");
    if (bad) throw std::runtime_error("bad wr pointer non null");

    ibv_wc wc;
    while (true) {
        int ret = ibv_poll_cq(inner->conn->send_cq, 1, &wc);
        if (ret == 1) break;
        if (ret < 0) throw std::runtime_error("failed to poll completion queue");
    }
    if (wc.status != IBV_WC_SUCCESS) throw std::runtime_error("work completion error");
}
