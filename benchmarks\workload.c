#include "workload.h"

int pth_workload_report_init(struct pth_workload_report *report)
{
	report->warmup_op_count = 0;
	report->op_count = 0;

	int rc = pthread_mutex_init(&report->mutex, NULL);
	if (rc != 0)
		return rc;

	report->duration = 0;

	return 0;
}

int pth_workload_context_init(struct pth_workload_context *ctx,
			      const struct pth_workload_config *cfg,
			      void *target,
			      struct pth_xoshiro256p *master_uniform,
			      struct pth_workload_report *report)
{
	ctx->target = target;
	ctx->config = cfg;

	ctx->uniform = *master_uniform;
	pth_xoshiro256p_jump(master_uniform);

	int rc = pth_zipf_init(&ctx->zipf, cfg->key_num, cfg->zipf_theta,
			       master_uniform);
	if (rc != 0)
		return rc;
	pth_xoshiro256p_jump(master_uniform);

	ctx->report = report;

	return 0;
}
