# LRU Cache Manager

A high-performance LRU (Least Recently Used) cache manager for disaggregated memory systems using RDMA for remote memory access.

## Features

- **LRU Eviction Policy**: Implements strict LRU eviction with O(1) operations
- **RDMA Integration**: Uses RDMA for high-performance remote memory access
- **Thread Safety**: All operations are thread-safe using mutexes (no atomic operations)
- **Multiple Memory Types**: Support for different memory types (small, medium, large, persistent, temporary)
- **Comprehensive Statistics**: Detailed cache and allocation statistics
- **Batch Operations**: Efficient batch get/put operations
- **Cache Coherence**: Address dependency tracking and cache invalidation
- **Prefetching**: Automatic and manual prefetching support
- **Debugging Support**: Memory leak detection and cache integrity checking

## API Overview

### Core APIs

The LRU cache manager provides three main APIs:

1. **Address Management** (`addr.h`): Address operations and cache control
2. **Memory Allocation** (`alloc.h`): Memory allocation and management
3. **Cache Operations** (`cache.h`): Advanced cache configuration and monitoring

### Key Functions

#### Memory Allocation
```c
void* disaggAlloc(size_t size);                    // Basic allocation
void disaggFree(void* gaddr);                      // Free memory
void* disaggAllocTyped(size_t size, lru_mem_type_t type);  // Typed allocation
void* disaggRealloc(void* gaddr, size_t new_size); // Reallocate memory
```

#### Cache Operations
```c
void* lru_cache_get(void* gaddr, size_t size);     // Get from cache
bool lru_cache_put(void* gaddr, const void* data, size_t size);  // Put in cache
bool lru_cache_remove(void* gaddr);                // Remove from cache
void lru_cache_flush(void* gaddr);                 // Flush to remote memory
```

#### Address Management
```c
void* getLocalAddr(void* gaddr);                   // Get local address
bool isLocalAddr(void* gaddr);                     // Check if cached
void lru_cache_touch(void* gaddr);                 // Mark as recently used
void lru_cache_prefetch(void* gaddr, size_t size); // Prefetch into cache
```

## Usage Example

```c
#include "lru/include/addr.h"
#include "lru/include/alloc.h"
#include "lru/include/cache.h"

int main() {
    // Initialize the LRU cache manager
    if (lru_alloc_init("*************") != 0) {
        fprintf(stderr, "Failed to initialize LRU cache\n");
        return 1;
    }
    
    // Configure cache
    lru_cache_config_t config = {
        .max_size = 1024 * 1024 * 1024,  // 1GB cache
        .max_entries = 100000,           // 100K entries max
        .eviction_policy = LRU_POLICY_STRICT_LRU,
        .write_policy = LRU_WRITE_BACK,
        .enable_prefetch = true,
        .prefetch_distance = 4096
    };
    lru_cache_set_config(&config);
    
    // Allocate memory
    void* ptr = disaggAlloc(4096);
    if (!ptr) {
        fprintf(stderr, "Allocation failed\n");
        return 1;
    }
    
    // Write data to cache
    char data[4096];
    memset(data, 0xAB, sizeof(data));
    lru_cache_put(ptr, data, sizeof(data));
    
    // Read data from cache
    void* cached_data = lru_cache_get(ptr, 4096);
    if (cached_data) {
        printf("Data retrieved from cache\n");
    }
    
    // Print statistics
    lru_cache_print_stats();
    disaggPrintStats();
    
    // Cleanup
    disaggFree(ptr);
    lru_alloc_cleanup();
    
    return 0;
}
```

## Memory Types

The cache manager supports different memory types for optimized handling:

- `LRU_MEM_TYPE_DEFAULT`: Default memory type
- `LRU_MEM_TYPE_SMALL`: Small objects (< 1KB)
- `LRU_MEM_TYPE_MEDIUM`: Medium objects (1KB - 64KB)
- `LRU_MEM_TYPE_LARGE`: Large objects (> 64KB)
- `LRU_MEM_TYPE_PERSISTENT`: Persistent/pinned memory
- `LRU_MEM_TYPE_TEMPORARY`: Temporary/scratch memory

## Cache Policies

### Eviction Policies
- `LRU_POLICY_STRICT_LRU`: Strict LRU eviction (default)
- `LRU_POLICY_LRU_K`: LRU-K algorithm
- `LRU_POLICY_ARC`: Adaptive Replacement Cache
- `LRU_POLICY_CLOCK`: Clock algorithm
- `LRU_POLICY_RANDOM`: Random eviction

### Write Policies
- `LRU_WRITE_THROUGH`: Write through to remote memory
- `LRU_WRITE_BACK`: Write back on eviction (default)
- `LRU_WRITE_AROUND`: Bypass cache on writes

## Building

```bash
mkdir build
cd build
cmake ..
make
```

### Requirements
- CMake 3.20+
- C++17 compiler
- libibverbs (RDMA verbs library)
- librdmacm (RDMA connection manager)
- pthread

## Testing

```bash
make run-lru-tests
```

## Performance Characteristics

- **Cache Operations**: O(1) get, put, remove operations
- **Memory Overhead**: ~64 bytes per cache entry
- **Thread Safety**: Mutex-based synchronization (no atomic operations)
- **RDMA Performance**: Direct memory access with minimal CPU overhead

## Configuration

The cache can be configured at runtime:

```c
lru_cache_config_t config = {
    .max_size = 2ULL * 1024 * 1024 * 1024,  // 2GB cache
    .max_entries = 1000000,                  // 1M entries
    .eviction_policy = LRU_POLICY_STRICT_LRU,
    .write_policy = LRU_WRITE_BACK,
    .enable_prefetch = true,
    .prefetch_distance = 8192,
    .enable_compression = false,
    .compression_ratio = 0.5
};
```

## Monitoring and Debugging

### Statistics
```c
lru_cache_stats_t stats;
lru_cache_get_stats(&stats);
printf("Hit rate: %.2f%%\n", stats.hit_rate * 100.0);
printf("Cache size: %zu bytes\n", stats.current_size);
```

### Debugging
```c
lru_cache_dump_entries();           // Dump all cache entries
lru_cache_validate_consistency();   // Validate cache consistency
disaggCheckLeaks();                 // Check for memory leaks
```

## Integration with Existing Code

The LRU cache manager provides APIs compatible with the existing `runtime/compute` module:

- `getLocalAddr()` and `isLocalAddr()` functions are compatible
- `disaggAlloc()` and `disaggFree()` provide the same interface
- Additional LRU-specific functions are prefixed with `lru_cache_`

## License

This project is part of the Pentathlon runtime system.
