# Compilation Scripts Comparison: Skiplist vs B+ Tree

This document compares the compilation approaches for skiplist and B+ tree data structures in the Pentathlon benchmark suite.

## Overview

Both data structures use the same MLIR/LLVM compilation pipeline but have different characteristics and optimizations.

## Script Comparison

| Aspect | Skiplist | B+ Tree |
|--------|----------|---------|
| **Source Language** | C++ (`skip_list.cxx`) | C (`bplus_tree.c`) |
| **Main Script** | `compile.sh` | `compile_bptree.sh` |
| **Optimized Script** | ❌ Not available | `compile_bptree_optimized.sh` |
| **Build Script** | ❌ Not available | `build_bptree.sh` |
| **Test Script** | ❌ Not available | `test_bptree_compilation.sh` |

## Compilation Pipeline

### Common Pipeline Stages
Both follow the same 4-stage pipeline:

1. **C/C++ → LLVM Dialect** (cgeist)
2. **LLVM Dialect → Optimized MLIR** (my-opt)
3. **MLIR → LLVM IR** (mlir-translate)
4. **LLVM IR → Assembly** (llc)

### Transformation Passes
Both use the same disaggregated memory passes:
- `--addr-dep-pass` - Address dependency analysis
- `--disagg-alloc-pass` - Disaggregated allocation
- `--disagg-free-pass` - Disaggregated deallocation
- `--local-addr-pass` - Local address optimization

## Key Differences

### 1. Source Code Characteristics

#### Skiplist
```cpp
// C++ implementation
class SkipList {
    // Object-oriented design
    // Template-based (potentially)
    // STL containers usage
};
```

#### B+ Tree
```c
// C implementation
typedef struct BPlusTree {
    // Struct-based design
    // Manual memory management
    // POSIX threads for concurrency
} BPlusTree;
```

### 2. Concurrency Models

#### Skiplist
- Likely uses C++ threading primitives
- May use atomic operations
- Object-oriented synchronization

#### B+ Tree
- Uses POSIX threads (`pthread`)
- Read-write locks (`pthread_rwlock_t`)
- Latch crabbing technique
- Explicit lock management

### 3. Memory Management

#### Skiplist
- C++ new/delete operators
- RAII patterns
- Smart pointers (potentially)

#### B+ Tree
- Manual malloc/free
- Explicit memory management
- Custom node allocation

### 4. Optimization Opportunities

#### Skiplist
```bash
# Basic optimization only
CGEIST_FLAGS="-emit-llvm-dialect -S -function=*"
MY_OPT_FLAGS="--addr-dep-pass --disagg-alloc-pass --disagg-free-pass --local-addr-pass"
```

#### B+ Tree
```bash
# Enhanced optimization
CGEIST_FLAGS="-emit-llvm-dialect -S -function=* -O2"
MY_OPT_FLAGS="--addr-dep-pass --disagg-alloc-pass --disagg-free-pass --local-addr-pass --canonicalize --cse --loop-unroll --mem2reg"
LLC_FLAGS="-O3 -march=native -mcpu=native"
```

## Performance Characteristics

### Skiplist
- **Probabilistic structure**: O(log n) expected performance
- **Simple implementation**: Fewer optimization opportunities
- **Cache behavior**: May have poor spatial locality

### B+ Tree
- **Deterministic structure**: Guaranteed O(log n) performance
- **Complex implementation**: More optimization opportunities
- **Cache behavior**: Better spatial locality due to node structure

## Generated Artifacts

### Skiplist Output
```
output/
├── skiplist_llvm_dialect.mlir
├── skiplist_llvm_dialect_fixed.mlir
├── skiplist_optimized.mlir
├── skiplist.ll
└── skiplist.s
```

### B+ Tree Output
```
output/
├── bptree_llvm_dialect.mlir
├── bptree_llvm_dialect_fixed.mlir
├── bptree_optimized.mlir
├── bptree.ll
├── bptree.s
├── bptree_optimized_*.mlir    # Optimized versions
├── bptree_optimized.ll
├── bptree_optimized.s
├── bptree_optimized.o
└── libbptree_optimized.a      # Static library
```

## Integration with Pentathlon

### CMake Integration

#### Skiplist
```cmake
# Basic integration
find_library(SKIPLIST_LIBRARIES
    NAMES libskiplist_optimized.a
    PATHS ../../benchmarks/scripts/output
)
target_link_libraries(pentathlon-bm-local ${SKIPLIST_LIBRARIES})
```

#### B+ Tree
```cmake
# Enhanced integration with multiple options
option(BPTREE_OPTIMIZED "Use optimized B+ tree" ON)

if(BPTREE_OPTIMIZED)
    target_link_libraries(pentathlon-bm-local libbptree_optimized.a)
else()
    target_link_libraries(pentathlon-bm-local libbptree.a)
endif()
```

### Runtime Integration

Both integrate with `runtime/compute` for disaggregated memory:

#### Memory Allocation Transformation
```c
// Original code
void* ptr = malloc(size);
free(ptr);

// After disaggregated memory passes
GlobalAddr gaddr = disaggAlloc(size);
void* ptr = getLocalAddr(gaddr);
disaggFree(gaddr);
```

## Benchmark API

Both provide similar benchmark interfaces:

### Skiplist API
```cpp
// Likely C++ style
SkipList* skiplist_create();
void skiplist_insert(SkipList* list, int key, int value);
bool skiplist_search(SkipList* list, int key, int* value);
void skiplist_delete(SkipList* list, int key);
```

### B+ Tree API
```c
// C style with explicit benchmark functions
void* pth_bm_target_create();
void pth_bm_target_read(void* target, int key);
void pth_bm_target_insert(void* target, int key);
void pth_bm_target_update(void* target, int key);
void pth_bm_target_delete(void* target, int key);
```

## Recommendations

### For Skiplist
1. **Add optimized compilation script** similar to B+ tree
2. **Create build automation** with multiple optimization levels
3. **Add static library generation** for easier linking
4. **Implement benchmark API** for consistent interface

### For B+ Tree
1. **Current implementation is comprehensive** ✓
2. **Consider adding profile-guided optimization** (PGO)
3. **Add link-time optimization** (LTO) support
4. **Create performance comparison tools**

## Usage Examples

### Building Skiplist
```bash
cd benchmarks/scripts
./compile.sh
```

### Building B+ Tree
```bash
cd benchmarks/scripts

# Build both versions
./build_bptree.sh both

# Build optimized only
./build_bptree.sh optimized

# Test compilation setup
./test_bptree_compilation.sh
```

## Future Enhancements

### Common Improvements
1. **Unified build system** for all data structures
2. **Performance profiling integration**
3. **Automated testing pipeline**
4. **Cross-compilation support**

### Data Structure Specific
1. **Skiplist**: Add concurrent operations, optimize for NUMA
2. **B+ Tree**: Add bulk loading, range query optimization
3. **Both**: Add memory pool allocation, custom allocators

This comparison shows that the B+ tree compilation setup is more mature and feature-complete, providing a good template for enhancing the skiplist compilation process.
