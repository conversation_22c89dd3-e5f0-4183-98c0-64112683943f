# Pentathlon Benchmark Quick Start

This guide shows how to quickly build and run the new Pentathlon benchmark targets.

## Overview

The benchmark now supports two new specialized targets:
- **`benchmark-optimized`** - Links with `libskiplist_optimized.a` + `libdm_compiler_rt_compute.a`
- **`lru`** - Links with `libskiplist_optimized.a` + `lru-cache`

## Quick Build Steps

### 1. Generate Optimized Skiplist (Optional)
```bash
cd scripts
./compile.sh
cd ..
```
This creates the optimized skiplist assembly. If skipped, regular skiplist will be used.

### 2. Build Runtime Libraries
```bash
./prepare_runtime_libs.sh
```
This builds and copies the required libraries to `runtime/build/`:
- `libdm_compiler_rt_compute.a`
- `liblru-cache.a`

### 3. Build Benchmark Targets
```bash
# Build optimized benchmark
./build_targets.sh benchmark-optimized

# Build LRU benchmark
./build_targets.sh lru

# Or build both
./build_targets.sh benchmark-optimized
./build_targets.sh lru
```

## Alternative: Manual CMake Build

```bash
mkdir build && cd build

# Configure with required components
cmake -DPENTATHLON_LINK_COMPUTE=ON -DPENTATHLON_LINK_LRU=ON ..

# Build specific targets
make benchmark-optimized
make lru
```

## Running the Benchmarks

```bash
# Run optimized benchmark
./build/pentathlon-bm-local-optimized [options]

# Run LRU benchmark
./build/pentathlon-bm-local-lru [options]
```

## Troubleshooting

### Missing Optimized Skiplist
```
Warning: libskiplist_optimized.a not available
```
**Solution**: Run `cd scripts && ./compile.sh` to generate optimized skiplist

### Missing Runtime Libraries
```
Warning: Pre-built compute library not found
```
**Solution**: Run `./prepare_runtime_libs.sh` to build runtime libraries

### Build Failures
```bash
# Clean and rebuild everything
./prepare_runtime_libs.sh --clean
./build_targets.sh clean
./build_targets.sh benchmark-optimized
```

## File Locations

After successful build:
```
runtime/build/
├── libdm_compiler_rt_compute.a     # Compute runtime library
└── liblru-cache.a                  # LRU cache library

scripts/output/
└── skiplist.s                      # Optimized skiplist assembly

benchmarks/build/
├── pentathlon-bm-local-optimized   # Optimized benchmark
└── pentathlon-bm-local-lru         # LRU benchmark
```

## Build Scripts

- **`prepare_runtime_libs.sh`** - Build runtime libraries
- **`build_targets.sh`** - Build benchmark targets
- **`build_targets.bat`** - Windows version of build script

## Available Targets

| Target | Executable | Libraries |
|--------|------------|-----------|
| `benchmark-optimized` | `pentathlon-bm-local-optimized` | `libskiplist_optimized.a` + `libdm_compiler_rt_compute.a` |
| `lru` | `pentathlon-bm-local-lru` | `libskiplist_optimized.a` + `lru-cache` |
| `benchmark-skiplist` | `pentathlon-bm-local` | Skiplist only |
| `benchmark-compute` | `pentathlon-bm-local` | Compute runtime only |
| `benchmark-all` | `pentathlon-bm-local` | All components |

## One-Line Build Commands

```bash
# Complete build from scratch
./prepare_runtime_libs.sh && ./build_targets.sh benchmark-optimized

# Build LRU benchmark
./prepare_runtime_libs.sh && ./build_targets.sh lru

# Build both new targets
./prepare_runtime_libs.sh && ./build_targets.sh benchmark-optimized && ./build_targets.sh lru
```

## Performance Notes

- **`benchmark-optimized`**: Best performance with MLIR/LLVM optimizations + RDMA
- **`lru`**: Efficient caching with O(1) LRU operations + optimized skiplist
- Both targets use optimized skiplist when available, fall back to regular skiplist otherwise
