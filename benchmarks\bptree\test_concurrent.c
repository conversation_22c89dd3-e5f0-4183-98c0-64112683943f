#define _GNU_SOURCE
#include <pthread.h>
#include <time.h>
#include <unistd.h>
#include "bplus_tree.h"

#define NUM_THREADS 8
#define OPERATIONS_PER_THREAD 1000
#define MAX_KEY 10000

typedef struct {
    BPlusTree *tree;
    int thread_id;
    int start_key;
    int end_key;
    int *results;
} ThreadData;

// Thread function for concurrent insertions
void *insert_worker(void *arg) {
    ThreadData *data = (ThreadData *)arg;
    int successful = 0;

    for (int i = data->start_key; i < data->end_key; i++) {
        if (bplus_tree_concurrent_insert(data->tree, i, i * 10)) {
            successful++;
        }

        // Small random delay to increase contention
        if (i % 100 == 0) {
            usleep(1);
        }
    }

    data->results[data->thread_id] = successful;
    return NULL;
}

// Thread function for concurrent searches
void *search_worker(void *arg) {
    ThreadData *data = (ThreadData *)arg;
    int successful = 0;

    for (int i = 0; i < OPERATIONS_PER_THREAD; i++) {
        int key = rand() % MAX_KEY;
        int value;
        if (bplus_tree_concurrent_search(data->tree, key, &value)) {
            successful++;
        }

        if (i % 100 == 0) {
            usleep(1);
        }
    }

    data->results[data->thread_id] = successful;
    return NULL;
}

// Thread function for mixed operations
void *mixed_worker(void *arg) {
    ThreadData *data = (ThreadData *)arg;
    int operations = 0;

    for (int i = 0; i < OPERATIONS_PER_THREAD; i++) {
        int operation = rand() % 3;
        int key = (data->thread_id * OPERATIONS_PER_THREAD) + i;

        switch (operation) {
            case 0:  // Insert
                if (bplus_tree_concurrent_insert(data->tree, key, key * 10)) {
                    operations++;
                }
                break;
            case 1:  // Search
            {
                int value;
                if (bplus_tree_concurrent_search(data->tree, key, &value)) {
                    operations++;
                }
            } break;
            case 2:  // Delete
                if (bplus_tree_concurrent_delete(data->tree, key)) {
                    operations++;
                }
                break;
        }

        if (i % 50 == 0) {
            usleep(1);
        }
    }

    data->results[data->thread_id] = operations;
    return NULL;
}

void test_concurrent_insertions() {
    printf("Testing Concurrent Insertions\n");
    printf("==============================\n");

    BPlusTree *tree = bplus_tree_create();
    if (!tree) {
        printf("Failed to create tree\n");
        return;
    }

    pthread_t threads[NUM_THREADS];
    ThreadData thread_data[NUM_THREADS];
    int results[NUM_THREADS];

    clock_t start = clock();

    // Create threads for concurrent insertions
    for (int i = 0; i < NUM_THREADS; i++) {
        thread_data[i].tree = tree;
        thread_data[i].thread_id = i;
        thread_data[i].start_key = i * (MAX_KEY / NUM_THREADS);
        thread_data[i].end_key = (i + 1) * (MAX_KEY / NUM_THREADS);
        thread_data[i].results = results;

        pthread_create(&threads[i], NULL, insert_worker, &thread_data[i]);
    }

    // Wait for all threads to complete
    for (int i = 0; i < NUM_THREADS; i++) {
        pthread_join(threads[i], NULL);
    }

    clock_t end = clock();
    double time_taken = ((double)(end - start)) / CLOCKS_PER_SEC;

    int total_insertions = 0;
    for (int i = 0; i < NUM_THREADS; i++) {
        total_insertions += results[i];
        printf("Thread %d: %d successful insertions\n", i, results[i]);
    }

    printf("Total insertions: %d\n", total_insertions);
    printf("Time taken: %.2f seconds\n", time_taken);
    printf("Insertions per second: %.2f\n", total_insertions / time_taken);

    // Verify tree structure
    printf("\nTree structure after concurrent insertions:\n");
    printf("Tree height: %d, nodes: %d\n", tree->height, tree->num_nodes);

    bplus_tree_destroy(tree);
    printf("Concurrent insertion test completed\n\n");
}

void test_concurrent_searches() {
    printf("Testing Concurrent Searches\n");
    printf("============================\n");

    BPlusTree *tree = bplus_tree_create();
    if (!tree) {
        printf("Failed to create tree\n");
        return;
    }

    // Pre-populate tree with some data
    printf("Pre-populating tree with %d keys...\n", MAX_KEY / 2);
    for (int i = 0; i < MAX_KEY / 2; i++) {
        bplus_tree_insert(tree, i, i * 10);
    }

    pthread_t threads[NUM_THREADS];
    ThreadData thread_data[NUM_THREADS];
    int results[NUM_THREADS];

    clock_t start = clock();

    // Create threads for concurrent searches
    for (int i = 0; i < NUM_THREADS; i++) {
        thread_data[i].tree = tree;
        thread_data[i].thread_id = i;
        thread_data[i].results = results;

        pthread_create(&threads[i], NULL, search_worker, &thread_data[i]);
    }

    // Wait for all threads to complete
    for (int i = 0; i < NUM_THREADS; i++) {
        pthread_join(threads[i], NULL);
    }

    clock_t end = clock();
    double time_taken = ((double)(end - start)) / CLOCKS_PER_SEC;

    int total_searches = 0;
    for (int i = 0; i < NUM_THREADS; i++) {
        total_searches += results[i];
        printf("Thread %d: %d successful searches\n", i, results[i]);
    }

    printf("Total successful searches: %d\n", total_searches);
    printf("Time taken: %.2f seconds\n", time_taken);
    printf("Searches per second: %.2f\n", (NUM_THREADS * OPERATIONS_PER_THREAD) / time_taken);

    bplus_tree_destroy(tree);
    printf("Concurrent search test completed\n\n");
}

void test_mixed_operations() {
    printf("Testing Mixed Concurrent Operations\n");
    printf("===================================\n");

    BPlusTree *tree = bplus_tree_create();
    if (!tree) {
        printf("Failed to create tree\n");
        return;
    }

    pthread_t threads[NUM_THREADS];
    ThreadData thread_data[NUM_THREADS];
    int results[NUM_THREADS];

    clock_t start = clock();

    // Create threads for mixed operations
    for (int i = 0; i < NUM_THREADS; i++) {
        thread_data[i].tree = tree;
        thread_data[i].thread_id = i;
        thread_data[i].results = results;

        pthread_create(&threads[i], NULL, mixed_worker, &thread_data[i]);
    }

    // Wait for all threads to complete
    for (int i = 0; i < NUM_THREADS; i++) {
        pthread_join(threads[i], NULL);
    }

    clock_t end = clock();
    double time_taken = ((double)(end - start)) / CLOCKS_PER_SEC;

    int total_operations = 0;
    for (int i = 0; i < NUM_THREADS; i++) {
        total_operations += results[i];
        printf("Thread %d: %d successful operations\n", i, results[i]);
    }

    printf("Total operations: %d\n", total_operations);
    printf("Time taken: %.2f seconds\n", time_taken);
    printf("Operations per second: %.2f\n", total_operations / time_taken);

    // Final tree state
    printf("\nFinal tree state:\n");
    printf("Tree height: %d, nodes: %d\n", tree->height, tree->num_nodes);

    bplus_tree_destroy(tree);
    printf("Mixed operations test completed\n\n");
}

int main() {
    printf("B+ Tree Concurrent Operations Test\n");
    printf("===================================\n\n");

    // Seed random number generator
    srand(time(NULL));

    // Run tests
    test_concurrent_insertions();
    test_concurrent_searches();
    test_mixed_operations();

    printf("All concurrent tests completed successfully!\n");
    return 0;
}
