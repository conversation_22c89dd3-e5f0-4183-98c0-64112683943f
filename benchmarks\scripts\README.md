# Skiplist Compilation Scripts

This directory contains compilation scripts for the skiplist benchmark using the MLIR/LLVM toolchain.

## Scripts Overview

### `compile.sh` - Main Compilation Pipeline

The main compilation script implements a 4-step compilation pipeline with automatic LLVM dialect fixing:

1. **cgeist**: Compiles C++ skiplist source to LLVM Dialect
2. **fix_llvm_dialect.sh**: Fixes LLVM dialect patterns (replaces `[]` with `[0]`)
3. **my-opt**: Applies custom MLIR optimizations to transform the LLVM Dialect
4. **mlir-translate**: Converts LLVM Dialect to LLVM IR
5. **llc**: Compiles LLVM IR to assembly code

### `fix_llvm_dialect.sh` - LLVM Dialect Pattern Fixer

A standalone script that checks and fixes LLVM dialect files by replacing all instances of `llvm.getelement ... []` with `llvm.getelement ... [0]`.

### `test_fix_llvm_dialect.sh` - Test Script

A test script that demonstrates the LLVM dialect fixer functionality with sample patterns.

## Main Pipeline Overview

The `compile.sh` script implements the enhanced compilation pipeline:

1. **cgeist**: Compiles C++ skiplist source to LLVM Dialect
2. **fix_llvm_dialect.sh**: Automatically fixes LLVM dialect patterns
3. **my-opt**: Applies custom MLIR optimizations to transform the LLVM Dialect
4. **mlir-translate**: Converts LLVM Dialect to LLVM IR
5. **llc**: Compiles LLVM IR to assembly code

## Prerequisites

Before running the script, ensure you have:

1. **Polygeist** installed at `~/Polygeist/llvm-project/build`
2. **Pentathlon compiler** built at `../../compiler/build` (relative to script location)
3. All required tools available:
   - `cgeist`
   - `my-opt` (custom MLIR optimization tool)
   - `mlir-translate`
   - `llc`

## Usage

### Main Compilation Pipeline

```bash
cd Pentathlon/benchmarks/scripts
./compile.sh
```

### LLVM Dialect Fixer (Standalone)

```bash
# Check for patterns only
./fix_llvm_dialect.sh -c -i input.mlir

# Fix patterns with backup
./fix_llvm_dialect.sh -i input.mlir -o output.mlir -b -v

# Use default paths (works with compile.sh output)
./fix_llvm_dialect.sh
```

### Test the Fixer

```bash
./test_fix_llvm_dialect.sh
```

## Configuration

The script can be customized by modifying the following variables:

- `POLYGEIST_PATH`: Path to Polygeist installation
- `COMPILER_BUILD_PATH`: Path to Pentathlon compiler build
- `OUTPUT_DIR`: Directory for output files
- `SKIPLIST_DIR`: Directory containing skiplist source files

## Output Files

The compilation pipeline generates the following files in the `./output` directory:

- `skiplist_llvm_dialect.mlir`: Initial LLVM Dialect from cgeist
- `skiplist_llvm_dialect_fixed.mlir`: Fixed LLVM Dialect (empty brackets replaced)
- `skiplist_optimized.mlir`: Optimized LLVM Dialect from my-opt
- `skiplist.ll`: LLVM IR from mlir-translate
- `skiplist.s`: Assembly code from llc

## LLVM Dialect Fixer Details

The `fix_llvm_dialect.sh` script addresses a common issue where cgeist generates LLVM dialect code with empty brackets in `llvm.getelement` instructions. This can cause issues in subsequent compilation steps.

### What it fixes:
- Replaces `llvm.getelement %ptr[]` with `llvm.getelement %ptr[0]`
- Handles complex type signatures
- Preserves existing non-empty bracket patterns

### Command-line options:
- `-i, --input FILE`: Specify input file
- `-o, --output FILE`: Specify output file
- `-c, --check-only`: Only analyze patterns, don't modify
- `-b, --backup`: Create backup of original file
- `-v, --verbose`: Enable verbose output
- `-h, --help`: Show help message

## Target Architecture

The script is configured for ARM64 (ARMv8-A) with Cortex-A53 CPU and NEON extensions:
- `-march=armv8-a`
- `-mcpu=cortex-a53`
- `-mattr=+neon`

## Error Handling

The script includes comprehensive error checking:
- Verifies tool availability before compilation
- Exits on any compilation step failure
- Provides detailed error messages

## Customization

To add additional source files, modify the `SKIPLIST_SOURCES` variable:

```bash
SKIPLIST_SOURCES="$SKIPLIST_DIR/skip_list.cxx $SKIPLIST_DIR/additional_file.cxx"
```

To modify compilation flags, edit the respective `*_FLAGS` variables.
