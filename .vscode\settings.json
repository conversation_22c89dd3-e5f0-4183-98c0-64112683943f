{"files.associations": {"cmath": "cpp", "cstdarg": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "type_traits": "cpp", "limits": "cpp", "typeinfo": "cpp", "new": "cpp", "mutex": "cpp", "stdexcept": "cpp", "vector": "cpp", "array": "cpp", "string": "cpp", "string_view": "cpp", "istream": "cpp", "ostream": "cpp", "ratio": "cpp", "sstream": "cpp", "streambuf": "cpp", "unordered_map": "cpp", "list": "cpp", "atomic": "cpp", "cctype": "cpp", "chrono": "cpp", "clocale": "cpp", "condition_variable": "cpp", "cstddef": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "system_error": "cpp", "tuple": "cpp", "utility": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iostream": "cpp", "thread": "cpp"}, "editor.tabSize": 4, "cmake.sourceDirectory": "/root/Pentathlon/runtime"}