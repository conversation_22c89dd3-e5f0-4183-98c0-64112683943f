#!/bin/bash

# Script to check and fix LLVM dialect compiled by cgeist
# Replaces all "llvm.getelement ... []" with "llvm.getelement ... [0]"

set -e  # Exit on any error

# Configuration
OUTPUT_DIR="./output"
LLVM_DIALECT_FILE="$OUTPUT_DIR/skiplist_llvm_dialect.mlir"
FIXED_DIALECT_FILE="$OUTPUT_DIR/skiplist_llvm_dialect_fixed.mlir"
BACKUP_FILE="$OUTPUT_DIR/skiplist_llvm_dialect_backup.mlir"

# Function to display usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -i, --input FILE     Input LLVM dialect file (default: $LLVM_DIALECT_FILE)"
    echo "  -o, --output FILE    Output fixed file (default: $FIXED_DIALECT_FILE)"
    echo "  -b, --backup         Create backup of original file"
    echo "  -c, --check-only     Only check for patterns, don't modify"
    echo "  -v, --verbose        Verbose output"
    echo "  -h, --help           Show this help message"
    exit 1
}

# Parse command line arguments
VERBOSE=false
CHECK_ONLY=false
CREATE_BACKUP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--input)
            LLVM_DIALECT_FILE="$2"
            shift 2
            ;;
        -o|--output)
            FIXED_DIALECT_FILE="$2"
            shift 2
            ;;
        -b|--backup)
            CREATE_BACKUP=true
            shift
            ;;
        -c|--check-only)
            CHECK_ONLY=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# Function for verbose output
log() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo "$@"
    fi
}

# Check if input file exists
if [[ ! -f "$LLVM_DIALECT_FILE" ]]; then
    echo "Error: Input file not found: $LLVM_DIALECT_FILE"
    echo "Make sure to run compile.sh first to generate the LLVM dialect file."
    exit 1
fi

echo "=== LLVM Dialect Fixer ==="
echo "Input file: $LLVM_DIALECT_FILE"
if [[ "$CHECK_ONLY" == "false" ]]; then
    echo "Output file: $FIXED_DIALECT_FILE"
fi
echo

# Create output directory if it doesn't exist
mkdir -p "$(dirname "$FIXED_DIALECT_FILE")"

# Check for patterns that need to be fixed
echo "Checking for llvm.getelement patterns..."

# Count occurrences of the pattern
EMPTY_BRACKET_COUNT=$(grep -c "llvm\.getelement.*\[\]" "$LLVM_DIALECT_FILE" || true)
ZERO_BRACKET_COUNT=$(grep -c "llvm\.getelement.*\[0\]" "$LLVM_DIALECT_FILE" || true)
TOTAL_GETELEMENT_COUNT=$(grep -c "llvm\.getelement" "$LLVM_DIALECT_FILE" || true)

echo "Pattern analysis:"
echo "  - Total llvm.getelement instructions: $TOTAL_GETELEMENT_COUNT"
echo "  - Instructions with empty brackets []: $EMPTY_BRACKET_COUNT"
echo "  - Instructions with [0] brackets: $ZERO_BRACKET_COUNT"
echo

if [[ $EMPTY_BRACKET_COUNT -eq 0 ]]; then
    echo "✓ No empty bracket patterns found. File appears to be correct."
    if [[ "$CHECK_ONLY" == "false" ]]; then
        echo "Copying original file to output location..."
        cp "$LLVM_DIALECT_FILE" "$FIXED_DIALECT_FILE"
    fi
    exit 0
fi

echo "Found $EMPTY_BRACKET_COUNT instances of llvm.getelement with empty brackets []"

if [[ "$CHECK_ONLY" == "true" ]]; then
    echo
    echo "=== Check-only mode: Showing problematic lines ==="
    grep -n "llvm\.getelement.*\[\]" "$LLVM_DIALECT_FILE" | head -10
    if [[ $EMPTY_BRACKET_COUNT -gt 10 ]]; then
        echo "... and $((EMPTY_BRACKET_COUNT - 10)) more lines"
    fi
    exit 0
fi

# Create backup if requested
if [[ "$CREATE_BACKUP" == "true" ]]; then
    log "Creating backup: $BACKUP_FILE"
    cp "$LLVM_DIALECT_FILE" "$BACKUP_FILE"
    echo "✓ Backup created: $BACKUP_FILE"
fi

# Perform the replacement
echo "Fixing llvm.getelement patterns..."
log "Replacing 'llvm.getelement ... []' with 'llvm.getelement ... [0]'"

# Use sed to perform the replacement
# This regex matches llvm.getelement followed by any characters and then []
sed 's/\(llvm\.getelement[^[]*\)\[\]/\1[0]/g' "$LLVM_DIALECT_FILE" > "$FIXED_DIALECT_FILE"

# Verify the fix
echo "Verifying fixes..."
NEW_EMPTY_BRACKET_COUNT=$(grep -c "llvm\.getelement.*\[\]" "$FIXED_DIALECT_FILE" || true)
NEW_ZERO_BRACKET_COUNT=$(grep -c "llvm\.getelement.*\[0\]" "$FIXED_DIALECT_FILE" || true)

echo "After fixing:"
echo "  - Instructions with empty brackets []: $NEW_EMPTY_BRACKET_COUNT"
echo "  - Instructions with [0] brackets: $NEW_ZERO_BRACKET_COUNT"
echo

if [[ $NEW_EMPTY_BRACKET_COUNT -eq 0 ]]; then
    echo "✓ All empty bracket patterns successfully fixed!"
    echo "✓ Fixed file saved: $FIXED_DIALECT_FILE"
    
    # Show file sizes
    ORIGINAL_SIZE=$(wc -c < "$LLVM_DIALECT_FILE")
    FIXED_SIZE=$(wc -c < "$FIXED_DIALECT_FILE")
    echo
    echo "File sizes:"
    echo "  - Original: $ORIGINAL_SIZE bytes"
    echo "  - Fixed: $FIXED_SIZE bytes"
    
    if [[ $ORIGINAL_SIZE -eq $FIXED_SIZE ]]; then
        echo "  - Size unchanged (good - only bracket contents modified)"
    fi
else
    echo "⚠ Warning: $NEW_EMPTY_BRACKET_COUNT empty bracket patterns still remain"
    echo "Manual inspection may be required."
fi

echo
echo "=== Fix Complete ==="
echo "You can now use the fixed file in your compilation pipeline:"
echo "  $FIXED_DIALECT_FILE"
