#ifndef PENTATHLON_BENCHMARKS_WORKLOAD_H_
#define PENTATHLON_BENCHMARKS_WORKLOAD_H_

#include <stdint.h>
#include <pthread.h>
#include <stdatomic.h>

#include "generators/xoshiro256p.h"
#include "generators/zipf.h"

struct pth_workload_config {
	int thread_num;
	uint64_t key_num;
	uint64_t warmup_op_num;
	uint64_t op_num;

	double zipf_theta;

	double read_ratio;
	double insert_ratio;
	double update_ratio;
	double delete_ratio;
};

struct pth_workload_report {
	_Atomic uint64_t warmup_op_count;
	_Atomic uint64_t op_count;

	pthread_mutex_t mutex;
	uint64_t duration;
};

int pth_workload_report_init(struct pth_workload_report *report);

struct pth_workload_context {
	void *target;
	const struct pth_workload_config *config;

	struct pth_xoshiro256p uniform;
	struct pth_zipf zipf;

	struct pth_workload_report *report;
};

int pth_workload_context_init(struct pth_workload_context *ctx,
			      const struct pth_workload_config *cfg,
			      void *target,
			      struct pth_xoshiro256p *master_uniform,
			      struct pth_workload_report *report);

#endif
