#include <cstdint>
#include <iostream>
#include <stdlib.h>
#include <time.h>
#include <random>
#include "addr.h"
#include "alloc.h"
#include "init.hpp"
#include "rdma.hpp"

struct node {
    size_t data;
    node *left, *right;
    node(size_t val) : data(val), left(nullptr), right(nullptr) {}
};

void insertBST(node *&root, size_t value) {
    if (root == nullptr) {
        void* gaddr = disaggAlloc(sizeof(node));
        if (gaddr == nullptr) {
            std::cerr << "Failed to allocate memory for new node." << std::endl;
            return;
        }
        root = reinterpret_cast<node *>(gaddr);
        void *local_addr = getLocalAddr(reinterpret_cast<void *>(root));
        if (local_addr == nullptr) {
            std::cerr << "Failed to get local address for new node." << std::endl;
            return;
        }
        node *current = reinterpret_cast<node *>(local_addr);
        current->data = value;
        current->left = nullptr;
        current->right = nullptr;
    } else {
        void *local_addr = getLocalAddr(reinterpret_cast<void *>(root));
        if (local_addr == nullptr) {
            std::cerr << "Failed to get local address for root node." << std::endl;
            return;
        }
        node *current = reinterpret_cast<node *>(local_addr);
        if (value < current->data) {
            insertBST(current->left, value);
        } else {
            insertBST(current->right, value);
        }
    }
}

void printInOrder(node *root) {
    if (root != nullptr) {
        void *local_addr = getLocalAddr(reinterpret_cast<void *>(root));
        if (local_addr == nullptr) {
            std::cerr << "Failed to get local address for node." << std::endl;
            return;
        }
        printInOrder(reinterpret_cast<node *>(local_addr)->left);
        std::cout << reinterpret_cast<node *>(local_addr)->data << " ";
        printInOrder(reinterpret_cast<node *>(local_addr)->right);
    }
}

int main() {
    // Generate a random tree
    std::srand(static_cast<unsigned int>(19260817));
    std::mt19937 rng(19260817);
    std::uniform_int_distribution<int> dist(1, 100);

    // Create the root
    node *root = nullptr;

    // Generate the rest of the tree
    for (int i = 0; i < 10; ++i) {
        size_t value = dist(rng);
        insertBST(root, value);
        printf("Inserted value: %zu\n", value);
    }

    // Print the tree
    printInOrder(root);
    return 0;
}
