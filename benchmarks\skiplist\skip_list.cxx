/**
    Implements the Concurrent Skip list data structure with insert, delete,
   search and range operations
*/

#include <climits>
#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>
#include <malloc.h>

#include "key_value_pair.hxx"
#include "list.hxx"
#include "skip_list.hxx"

static int max_level;

struct MakeshiftList {
    Node *value;
    MakeshiftList *next;

    bool contains(Node *value) {
        for (auto x = this; x; x = x->next) {
            if (x->value == value) return true;
        }
        return false;
    }
};

/**
    Constructor
*/
SkipList::SkipList(int max_elements, float prob) {
    max_level = (int)round(log(max_elements) / log(1 / prob)) - 1;
    head = new Node(INT_MIN, nullptr, max_level);
    tail = new Node(INT_MAX, nullptr, max_level);

    for (auto x = head->next.head; x; x = x->next) {
        x->value = tail;
    }
}

/**
    Finds the predecessors and successors at each level of where a given key
   exists or might exist. Updates the references in the vector using pass by
   reference. Returns -1 if not the key does not exist.
*/
int SkipList::find(int key, Node **predecessors, Node **successors) {
    int found = -1;
    Node *prev = head;

    for (int level = max_level; level >= 0; level--) {
        Node *curr = prev->next[level];

        while (key > curr->get_key()) {
            prev = curr;
            curr = prev->next[level];
        }

        if (found == -1 && key == curr->get_key()) {
            found = level;
        }

        predecessors[level] = prev;
        successors[level] = curr;
    }
    return found;
}

/**
    Randomly generates a number and increments level if number less than or
   equal to 0.5 Once more than 0.5, returns the level or available max level.
    This decides until which level a new Node is available.
*/
int SkipList::get_random_level() {
    int l = 0;
    while (static_cast<float>(rand()) / static_cast<float>(RAND_MAX) <= 0.5) {
        l++;
    }
    return l > max_level ? max_level : l;
}

/**
    Inserts into the Skip list at the appropriate place using locks.
    Return if already exists.
*/
bool SkipList::add(int key, const uint8_t* value) {
    // Get the level until which the new node must be available
    int top_level = get_random_level();

    // Initialization of references of the predecessors and successors
    Node *preds[max_level + 1];
    Node *succs[max_level + 1];

    for (size_t i = 0; i < max_level + 1; i++) {
        preds[i] = nullptr;
        succs[i] = nullptr;
    }

    // Keep trying to insert the element into the list. In case predecessors and
    // successors are changed, this loop helps to try the insert again
    while (true) {
        // Find the predecessors and successors of where the key must be inserted
        int found = find(key, preds, succs);

        // If found and marked, wait and continue insert
        // If found and unmarked, wait until it is fully_linked and return. No
        // insert needed If not found, go ahead with insert
        if (found != -1) {
            Node *node_found = succs[found];

            if (!node_found->marked) {
                while (!node_found->fully_linked) {
                }
                return false;
            }
            continue;
        }

        // Store all the Nodes which lock we acquire in a map
        // Map used so that we don't try to acquire lock to a Node we have already
        // acquired This may happen when we have the same predecessor at different
        // levels
        MakeshiftList *locked_nodes = nullptr;

        // Traverse the skip list and try to acquire the lock of predecessor at
        // every level
        try {
            // Used to check if the predecessor and successors are same from when we
            // tried to read them before
            bool valid = true;

            for (int level = 0; valid && (level <= top_level); level++) {
                auto pred = preds[level];
                auto succ = succs[level];

                // If not already acquired lock, then acquire the lock
                if (locked_nodes == nullptr || !(locked_nodes->contains(pred))) {
                    pred->lock();
                    MakeshiftList *new_node = (MakeshiftList *)malloc(sizeof(MakeshiftList));
                    new_node->value = pred;
                    new_node->next = locked_nodes;
                    locked_nodes = new_node;
                }

                // If predecessor marked or if the predecessor and successors change,
                // then abort and try again
                valid = !(pred->marked) &&
                        !(succ->marked) &&
                        pred->next[level] == succ;
            }

            // Conditons are not met, release locks, abort and try again.
            if (!valid) {
                for (auto x = locked_nodes; x; x = x->next) {
                    x->value->unlock();
                }
                continue;
            }

            // All conditions satisfied, create the Node and insert it as we have all
            // the required locks
            Node *new_node = new Node(key, value, top_level);

            // Update the predecessor and successors
            for (int level = 0; level <= top_level; level++) {
                new_node->next[level] = succs[level];
            }

            for (int level = 0; level <= top_level; level++) {
                preds[level]->next[level] = new_node;
            }

            // Mark the node as completely linked.
            new_node->fully_linked = true;

            // Release lock of all the nodes held once insert is complete
            for (auto x = locked_nodes; x; x = x->next) {
                x->value->unlock();
            }

            return true;
        } catch (const std::exception &e) {
            // If any exception occurs during the above insert, release locks of the
            // held nodes and try again.
            std::cerr << e.what() << '\n';
            for (auto x = locked_nodes; x; x = x->next) {
                x->value->unlock();
            }
        }
    }
}

/**
    Performs search to find if a node exists.
    Return value if the key found, else return nullptr
*/
const uint8_t* SkipList::search(int key) {
    // Finds the predecessor and successors

    Node *preds[max_level + 1];
    Node *succs[max_level + 1];

    for (size_t i = 0; i < max_level + 1; i++) {
        preds[i] = nullptr;
        succs[i] = nullptr;
    }

    int found = find(key, preds, succs);

    // If not found return nullptr.
    if (found == -1) {
        return nullptr;
    }

    Node *curr = head;

    for (int level = max_level; level >= 0; level--) {
        while (curr->next[level] != NULL && key > curr->next[level]->get_key()) {
            curr = curr->next[level];
        }
    }

    curr = curr->next[0];

    // If found, unmarked and fully linked, then return value. Else return nullptr.
    if ((curr != NULL) && (curr->get_key() == key) && succs[found]->fully_linked &&
        !succs[found]->marked) {
        return curr->get_value();
    } else {
        return nullptr;
    }
}

/**
    Deletes from the Skip list at the appropriate place using locks.
    Return if key doesn’t exist in the list.
*/
bool SkipList::remove(int key) {
    // Initialization
    Node *victim = NULL;
    bool is_marked = false;
    int top_level = -1;

    // Initialization of references of the predecessors and successors
    Node *preds[max_level + 1];
    Node *succs[max_level + 1];

    for (size_t i = 0; i < max_level + 1; i++) {
        preds[i] = nullptr;
        succs[i] = nullptr;
    }

    // Keep trying to delete the element from the list. In case predecessors and
    // successors are changed, this loop helps to try the delete again
    while (true) {
        // Find the predecessors and successors of where the key to be deleted
        int found = find(key, preds, succs);

        // If found, select the node to delete. else return
        if (found != -1) {
            victim = succs[found];
        }

        // If node not found and the node to be deleted is fully linked and not
        // marked return
        if (is_marked || (found != -1 && (victim->fully_linked && victim->top_level == found &&
                                          !(victim->marked)))) {
            // If not marked, the we lock the node and mark the node to delete
            if (!is_marked) {
                top_level = victim->top_level;
                victim->lock();
                if (victim->marked) {
                    victim->unlock();
                    return false;
                }
                victim->marked = true;
                is_marked = true;
            }

            // Store all the Nodes which lock we acquire in a map
            // Map used so that we don't try to acquire lock to a Node we have already
            // acquired This may happen when we have the same predecessor at different
            // levels
            MakeshiftList *locked_nodes = nullptr;

            // Traverse the skip list and try to acquire the lock of predecessor at
            // every level
            try {
                Node *pred;
                // Node* succ;

                // Used to check if the predecessors are not marked for delete and if
                // the predecessor next is the node we are trying to delete or if it is
                // changed.
                bool valid = true;

                for (int level = 0; valid && (level <= top_level); level++) {
                    pred = preds[level];

                    // If not already acquired lock, then acquire the lock
                    if (locked_nodes == nullptr || !(locked_nodes->contains(pred))) {
                        pred->lock();
                        MakeshiftList *new_node = (MakeshiftList *)malloc(sizeof(MakeshiftList));
                        new_node->value = pred;
                        new_node->next = locked_nodes;
                        locked_nodes = new_node;
                    }

                    // If predecessor marked or if the predecessor's next has changed,
                    // then abort and try again
                    valid = !(pred->marked) && pred->next[level] == victim;
                }

                // Conditons are not met, release locks, abort and try again.
                if (!valid) {
                    for (auto x = locked_nodes; x; x = x->next) {
                        x->value->unlock();
                    }
                    continue;
                }

                // All conditions satisfied, delete the Node and link them to the
                // successors appropriately
                for (int level = top_level; level >= 0; level--) {
                    preds[level]->next[level] = victim->next[level];
                }

                victim->unlock();

                // delete victim;

                // Delete is completed, release the locks held.
                for (auto x = locked_nodes; x; x = x->next) {
                    x->value->unlock();
                }

                return true;
            } catch (const std::exception &e) {
                // If any exception occurs during the above delete, release locks of the
                // held nodes and try again.
                for (auto x = locked_nodes; x; x = x->next) {
                    x->value->unlock();
                }
            }

        } else {
            return false;
        }
    }
}

/**
    Searches for the start_key in the skip list by traversing once we reach a
   point closer to start_key reaches to level 0 to find all keys between
   start_key and end_key. If search exceeds end, then abort Updates and returns
   the key value pairs in a map.
*/
// std::map<int, uint8_t*> SkipList::range(int start_key, int end_key) {
//     std::map<int, uint8_t*> range_output;

//     if (start_key > end_key) {
//         return range_output;
//     }

//     Node *curr = head;

//     for (int level = max_level; level >= 0; level--) {
//         while (curr->next[level] != NULL && start_key > curr->next[level]->get_key()) {
//             if (curr->get_key() >= start_key && curr->get_key() <= end_key) {
//                 range_output.insert(std::make_pair(curr->get_key(), curr->get_value()));
//             }
//             curr = curr->next[level];
//         }
//     }

//     while (curr != NULL && end_key >= curr->get_key()) {
//         if (curr->get_key() >= start_key && curr->get_key() <= end_key) {
//             range_output.insert(std::make_pair(curr->get_key(), curr->get_value()));
//         }
//         curr = curr->next[0];
//     }

//     return range_output;
// }

/**
    Display the skip list in readable format
*/
void SkipList::display() {
    for (int i = 0; i <= max_level; i++) {
        Node *temp = head;
        int count = 0;
        if (!(temp->get_key() == INT_MIN &&
              temp->next[i]->get_key() == INT_MAX)) {
            printf("Level %d  ", i);
            while (temp != NULL) {
                printf("%d -> ", temp->get_key());
                temp = temp->next[i];
                count++;
            }
            std::cout << std::endl;
        }
        if (count == 3) break;
    }
    printf("---------- Display done! ----------\n\n");
}

SkipList::SkipList() {
    head = tail = nullptr;
}

SkipList::~SkipList() {}

extern "C" {
void *pth_bm_target_create() { return new SkipList(1048576, 0.5); }

void pth_bm_target_destroy(void *target) { delete (SkipList *)target; }

void pth_bm_target_read(void *target, int key) {
    SkipList *list = (SkipList *)target;
    list->search(key);
}

void pth_bm_target_insert(void *target, int key) {
    SkipList *list = (SkipList *)target;
    list->add(key, nullptr);
}

void pth_bm_target_update(void *target, int key) {}

void pth_bm_target_delete(void *target, int key) {
    SkipList *list = (SkipList *)target;
    list->remove(key);
}
}
