; ModuleID = 'LLVMDialectModule'
source_filename = "LLVMDialectModule"
target datalayout = "e-m:e-p270:32:32-p271:32:32-p272:64:64-i64:64-f80:128-n8:16:32:64-S128"
target triple = "x86_64-unknown-linux-gnu"

%"_Converted_opaque@polygeist@<EMAIL>" = type { ptr, i32, i32, { { i32, i32, i32, i32, i32, i32, i32, i32, i8, [7 x i8], i64, i32, [4 x i8] } }, { { i32, i32, i32, i32, i32, i16, i16, { ptr, ptr } } } }
%"_Converted_opaque@polygeist@<EMAIL>" = type { i32, i32, [332 x i32], [333 x ptr], ptr, ptr, ptr, { { i32, i32, i32, i32, i32, i32, i32, i32, i8, [7 x i8], i64, i32, [4 x i8] } }, i8 }

@str17 = internal constant [42 x i8] c"B+ Tree (order=%d, height=%d, nodes=%d):\0A\00"
@str16 = internal constant [14 x i8] c"Tree is NULL\0A\00"
@str15 = internal constant [3 x i8] c"]\0A\00"
@str14 = internal constant [3 x i8] c", \00"
@str13 = internal constant [4 x i8] c":%d\00"
@str12 = internal constant [3 x i8] c"%d\00"
@str11 = internal constant [9 x i8] c"INTERNAL\00"
@str10 = internal constant [5 x i8] c"LEAF\00"
@str9 = internal constant [6 x i8] c"%s: [\00"
@str8 = internal constant [3 x i8] c"  \00"
@str7 = internal constant [2 x i8] c"\0A\00"
@str6 = internal constant [9 x i8] c"(%d:%d) \00"
@str5 = internal constant [17 x i8] c"Leaf traversal: \00"
@str4 = internal constant [15 x i8] c"Tree is empty\0A\00"
@str3 = internal constant [41 x i8] c"Error: Failed to initialize node rwlock\0A\00"
@str2 = internal constant [40 x i8] c"Error: Failed to initialize root mutex\0A\00"
@str1 = internal constant [41 x i8] c"Error: Failed to initialize tree rwlock\0A\00"
@str0 = internal constant [33 x i8] c"Error: Memory allocation failed\0A\00"

declare ptr @malloc(i64)

declare void @free(ptr)

declare ptr @getLocalAddr(ptr)

declare void @disaggFree(ptr)

declare ptr @disaggAlloc(i64)

declare void @acceptAddrDep(ptr)

declare void @addAddrDep(ptr, ptr)

declare i32 @printf(ptr, ...)

define ptr @bplus_tree_create() {
  %1 = alloca ptr, i64 1, align 8
  %2 = call ptr @disaggAlloc(i64 112)
  %3 = icmp eq ptr %2, null
  br i1 %3, label %4, label %8

4:                                                ; preds = %0
  %5 = call i32 (ptr, ...) @printf(ptr @str0)
  %6 = getelementptr ptr, ptr %1, i32 0
  call void @addAddrDep(ptr %6, ptr null)
  %7 = call ptr @getLocalAddr(ptr %6)
  store ptr null, ptr %7, align 8
  br label %44

8:                                                ; preds = %0
  call void @addAddrDep(ptr %2, ptr null)
  %9 = call ptr @getLocalAddr(ptr %2)
  store ptr null, ptr %9, align 8
  %10 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %2, i32 0, i32 1
  %11 = call ptr @getLocalAddr(ptr %10)
  store i32 0, ptr %11, align 4
  %12 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %2, i32 0, i32 2
  %13 = call ptr @getLocalAddr(ptr %12)
  store i32 0, ptr %13, align 4
  %14 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %2, i32 0, i32 3
  %15 = call ptr @getLocalAddr(ptr %14)
  %16 = call ptr @getLocalAddr(ptr null)
  %17 = call i32 @pthread_rwlock_init(ptr %15, ptr %16)
  %18 = icmp ne i32 %17, 0
  %19 = icmp eq i32 %17, 0
  br i1 %18, label %20, label %24

20:                                               ; preds = %8
  %21 = call i32 (ptr, ...) @printf(ptr @str1)
  call void @disaggFree(ptr %2)
  %22 = getelementptr ptr, ptr %1, i32 0
  call void @addAddrDep(ptr %22, ptr null)
  %23 = call ptr @getLocalAddr(ptr %22)
  store ptr null, ptr %23, align 8
  br label %24

24:                                               ; preds = %20, %8
  br i1 %19, label %25, label %43

25:                                               ; preds = %24
  %26 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %2, i32 0, i32 4
  %27 = call ptr @getLocalAddr(ptr %26)
  %28 = call ptr @getLocalAddr(ptr null)
  %29 = call i32 @pthread_mutex_init(ptr %27, ptr %28)
  %30 = icmp ne i32 %29, 0
  %31 = icmp eq i32 %29, 0
  br i1 %30, label %32, label %38

32:                                               ; preds = %25
  %33 = call i32 (ptr, ...) @printf(ptr @str2)
  %34 = call ptr @getLocalAddr(ptr %14)
  %35 = call i32 @pthread_rwlock_destroy(ptr %34)
  call void @disaggFree(ptr %2)
  %36 = getelementptr ptr, ptr %1, i32 0
  call void @addAddrDep(ptr %36, ptr null)
  %37 = call ptr @getLocalAddr(ptr %36)
  store ptr null, ptr %37, align 8
  br label %38

38:                                               ; preds = %32, %25
  br i1 %31, label %39, label %42

39:                                               ; preds = %38
  %40 = getelementptr ptr, ptr %1, i32 0
  call void @addAddrDep(ptr %40, ptr %2)
  %41 = call ptr @getLocalAddr(ptr %40)
  store ptr %2, ptr %41, align 8
  br label %42

42:                                               ; preds = %39, %38
  br label %43

43:                                               ; preds = %42, %24
  br label %44

44:                                               ; preds = %4, %43
  %45 = getelementptr ptr, ptr %1, i32 0
  call void @acceptAddrDep(ptr %45)
  %46 = call ptr @getLocalAddr(ptr %45)
  %47 = load ptr, ptr %46, align 8
  ret ptr %47
}

declare i32 @pthread_rwlock_init(ptr, ptr)

declare i32 @pthread_mutex_init(ptr, ptr)

declare i32 @pthread_rwlock_destroy(ptr)

define ptr @bplus_node_create(i32 %0) {
  %2 = alloca ptr, i64 1, align 8
  %3 = call ptr @disaggAlloc(i64 4088)
  %4 = icmp eq ptr %3, null
  br i1 %4, label %5, label %9

5:                                                ; preds = %1
  %6 = call i32 (ptr, ...) @printf(ptr @str0)
  %7 = getelementptr ptr, ptr %2, i32 0
  call void @addAddrDep(ptr %7, ptr null)
  %8 = call ptr @getLocalAddr(ptr %7)
  store ptr null, ptr %8, align 8
  br label %76

9:                                                ; preds = %1
  %10 = call ptr @getLocalAddr(ptr %3)
  store i32 %0, ptr %10, align 4
  %11 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %3, i32 0, i32 1
  %12 = call ptr @getLocalAddr(ptr %11)
  store i32 0, ptr %12, align 4
  %13 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %3, i32 0, i32 6
  call void @addAddrDep(ptr %13, ptr null)
  %14 = call ptr @getLocalAddr(ptr %13)
  store ptr null, ptr %14, align 8
  %15 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %3, i32 0, i32 5
  call void @addAddrDep(ptr %15, ptr null)
  %16 = call ptr @getLocalAddr(ptr %15)
  store ptr null, ptr %16, align 8
  %17 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %3, i32 0, i32 8
  %18 = call ptr @getLocalAddr(ptr %17)
  store i8 0, ptr %18, align 1
  %19 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %3, i32 0, i32 7
  %20 = call ptr @getLocalAddr(ptr %19)
  %21 = call ptr @getLocalAddr(ptr null)
  %22 = call i32 @pthread_rwlock_init(ptr %20, ptr %21)
  %23 = icmp ne i32 %22, 0
  %24 = icmp eq i32 %22, 0
  br i1 %23, label %25, label %29

25:                                               ; preds = %9
  %26 = call i32 (ptr, ...) @printf(ptr @str3)
  call void @disaggFree(ptr %3)
  %27 = getelementptr ptr, ptr %2, i32 0
  call void @addAddrDep(ptr %27, ptr null)
  %28 = call ptr @getLocalAddr(ptr %27)
  store ptr null, ptr %28, align 8
  br label %29

29:                                               ; preds = %25, %9
  br i1 %24, label %30, label %75

30:                                               ; preds = %29
  %31 = icmp eq i32 %0, 0
  br i1 %31, label %32, label %46

32:                                               ; preds = %30
  %33 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %3, i32 0, i32 4
  call void @addAddrDep(ptr %33, ptr null)
  %34 = call ptr @getLocalAddr(ptr %33)
  store ptr null, ptr %34, align 8
  %35 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %3, i32 0, i32 3
  br label %36

36:                                               ; preds = %39, %32
  %37 = phi i64 [ %43, %39 ], [ 0, %32 ]
  %38 = icmp slt i64 %37, 333
  br i1 %38, label %39, label %44

39:                                               ; preds = %36
  %40 = trunc i64 %37 to i32
  %41 = getelementptr ptr, ptr %35, i32 %40
  call void @addAddrDep(ptr %41, ptr null)
  %42 = call ptr @getLocalAddr(ptr %41)
  store ptr null, ptr %42, align 8
  %43 = add i64 %37, 1
  br label %36

44:                                               ; preds = %36, %54, %67
  %45 = phi i1 [ %53, %67 ], [ %53, %54 ], [ true, %36 ]
  br label %68

46:                                               ; preds = %30
  %47 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %3, i32 0, i32 4
  %48 = call ptr @disaggAlloc(i64 1328)
  call void @addAddrDep(ptr %47, ptr %48)
  %49 = call ptr @getLocalAddr(ptr %47)
  store ptr %48, ptr %49, align 8
  call void @acceptAddrDep(ptr %47)
  %50 = call ptr @getLocalAddr(ptr %47)
  %51 = load ptr, ptr %50, align 8
  %52 = icmp eq ptr %51, null
  %53 = xor i1 %52, true
  br i1 %52, label %54, label %57

54:                                               ; preds = %46
  call void @disaggFree(ptr %3)
  %55 = getelementptr ptr, ptr %2, i32 0
  call void @addAddrDep(ptr %55, ptr null)
  %56 = call ptr @getLocalAddr(ptr %55)
  store ptr null, ptr %56, align 8
  br label %44

57:                                               ; preds = %46
  br label %58

58:                                               ; preds = %61, %57
  %59 = phi i64 [ %66, %61 ], [ 0, %57 ]
  %60 = icmp slt i64 %59, 332
  br i1 %60, label %61, label %67

61:                                               ; preds = %58
  call void @acceptAddrDep(ptr %47)
  %62 = call ptr @getLocalAddr(ptr %47)
  %63 = load ptr, ptr %62, align 8
  %64 = getelementptr i32, ptr %63, i64 %59
  %65 = call ptr @getLocalAddr(ptr %64)
  store i32 0, ptr %65, align 4
  %66 = add i64 %59, 1
  br label %58

67:                                               ; preds = %58
  br label %44

68:                                               ; preds = %44
  %69 = phi i1 [ %45, %44 ]
  br label %70

70:                                               ; preds = %68
  br i1 %69, label %71, label %74

71:                                               ; preds = %70
  %72 = getelementptr ptr, ptr %2, i32 0
  call void @addAddrDep(ptr %72, ptr %3)
  %73 = call ptr @getLocalAddr(ptr %72)
  store ptr %3, ptr %73, align 8
  br label %74

74:                                               ; preds = %71, %70
  br label %75

75:                                               ; preds = %74, %29
  br label %76

76:                                               ; preds = %5, %75
  %77 = getelementptr ptr, ptr %2, i32 0
  call void @acceptAddrDep(ptr %77)
  %78 = call ptr @getLocalAddr(ptr %77)
  %79 = load ptr, ptr %78, align 8
  ret ptr %79
}

define void @bplus_node_destroy(ptr %0) {
  %2 = icmp eq ptr %0, null
  %3 = xor i1 %2, true
  br i1 %3, label %4, label %37

4:                                                ; preds = %1
  %5 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 8
  %6 = call ptr @getLocalAddr(ptr %5)
  store i8 1, ptr %6, align 1
  call void @acceptAddrDep(ptr %0)
  %7 = call ptr @getLocalAddr(ptr %0)
  %8 = load i32, ptr %7, align 4
  %9 = icmp eq i32 %8, 0
  br i1 %9, label %10, label %25

10:                                               ; preds = %4
  %11 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  %12 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 3
  br label %13

13:                                               ; preds = %18, %10
  %14 = phi i32 [ %23, %18 ], [ 0, %10 ]
  call void @acceptAddrDep(ptr %11)
  %15 = call ptr @getLocalAddr(ptr %11)
  %16 = load i32, ptr %15, align 4
  %17 = icmp sle i32 %14, %16
  br i1 %17, label %18, label %24

18:                                               ; preds = %13
  %19 = phi i32 [ %14, %13 ]
  %20 = getelementptr ptr, ptr %12, i32 %19
  call void @acceptAddrDep(ptr %20)
  %21 = call ptr @getLocalAddr(ptr %20)
  %22 = load ptr, ptr %21, align 8
  call void @bplus_node_destroy(ptr %22)
  %23 = add i32 %19, 1
  br label %13

24:                                               ; preds = %13
  br label %25

25:                                               ; preds = %24, %4
  %26 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 4
  call void @acceptAddrDep(ptr %26)
  %27 = call ptr @getLocalAddr(ptr %26)
  %28 = load ptr, ptr %27, align 8
  %29 = icmp ne ptr %28, null
  br i1 %29, label %30, label %33

30:                                               ; preds = %25
  call void @acceptAddrDep(ptr %26)
  %31 = call ptr @getLocalAddr(ptr %26)
  %32 = load ptr, ptr %31, align 8
  call void @disaggFree(ptr %32)
  br label %33

33:                                               ; preds = %30, %25
  %34 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 7
  %35 = call ptr @getLocalAddr(ptr %34)
  %36 = call i32 @pthread_rwlock_destroy(ptr %35)
  call void @disaggFree(ptr %0)
  br label %37

37:                                               ; preds = %33, %1
  ret void
}

define void @bplus_tree_destroy(ptr %0) {
  %2 = icmp eq ptr %0, null
  %3 = xor i1 %2, true
  br i1 %3, label %4, label %17

4:                                                ; preds = %1
  %5 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 3
  %6 = call ptr @getLocalAddr(ptr %5)
  %7 = call i32 @pthread_rwlock_wrlock(ptr %6)
  call void @acceptAddrDep(ptr %0)
  %8 = call ptr @getLocalAddr(ptr %0)
  %9 = load ptr, ptr %8, align 8
  call void @bplus_node_destroy(ptr %9)
  %10 = call ptr @getLocalAddr(ptr %5)
  %11 = call i32 @pthread_rwlock_unlock(ptr %10)
  %12 = call ptr @getLocalAddr(ptr %5)
  %13 = call i32 @pthread_rwlock_destroy(ptr %12)
  %14 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 4
  %15 = call ptr @getLocalAddr(ptr %14)
  %16 = call i32 @pthread_mutex_destroy(ptr %15)
  call void @disaggFree(ptr %0)
  br label %17

17:                                               ; preds = %4, %1
  ret void
}

declare i32 @pthread_rwlock_wrlock(ptr)

declare i32 @pthread_rwlock_unlock(ptr)

declare i32 @pthread_mutex_destroy(ptr)

define i8 @bplus_node_is_full(ptr %0) {
  %2 = icmp eq ptr %0, null
  br i1 %2, label %3, label %4

3:                                                ; preds = %1
  br label %10

4:                                                ; preds = %1
  %5 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  call void @acceptAddrDep(ptr %5)
  %6 = call ptr @getLocalAddr(ptr %5)
  %7 = load i32, ptr %6, align 4
  %8 = icmp sge i32 %7, 331
  %9 = zext i1 %8 to i8
  br label %10

10:                                               ; preds = %3, %4
  %11 = phi i8 [ %9, %4 ], [ 0, %3 ]
  br label %12

12:                                               ; preds = %10
  ret i8 %11
}

define i8 @bplus_node_is_minimal(ptr %0) {
  %2 = icmp eq ptr %0, null
  br i1 %2, label %3, label %4

3:                                                ; preds = %1
  br label %10

4:                                                ; preds = %1
  %5 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  call void @acceptAddrDep(ptr %5)
  %6 = call ptr @getLocalAddr(ptr %5)
  %7 = load i32, ptr %6, align 4
  %8 = icmp sge i32 %7, 165
  %9 = zext i1 %8 to i8
  br label %10

10:                                               ; preds = %3, %4
  %11 = phi i8 [ %9, %4 ], [ 0, %3 ]
  br label %12

12:                                               ; preds = %10
  ret i8 %11
}

define i32 @bplus_node_find_key_index(ptr %0, i32 %1) {
  %3 = icmp eq ptr %0, null
  br i1 %3, label %4, label %6

4:                                                ; preds = %30, %2
  %5 = phi i32 [ %29, %30 ], [ -1, %2 ]
  br label %31

6:                                                ; preds = %2
  %7 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  call void @acceptAddrDep(ptr %7)
  %8 = call ptr @getLocalAddr(ptr %7)
  %9 = load i32, ptr %8, align 4
  br label %10

10:                                               ; preds = %30, %6
  %11 = phi i32 [ %29, %30 ], [ 0, %6 ]
  %12 = icmp slt i32 %11, %9
  br i1 %12, label %13, label %24

13:                                               ; preds = %10
  %14 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 2
  %15 = getelementptr i32, ptr %14, i32 %11
  call void @acceptAddrDep(ptr %15)
  %16 = call ptr @getLocalAddr(ptr %15)
  %17 = load i32, ptr %16, align 4
  %18 = icmp slt i32 %17, %1
  br i1 %18, label %19, label %21

19:                                               ; preds = %13
  %20 = add i32 %11, 1
  br label %22

21:                                               ; preds = %13
  br label %22

22:                                               ; preds = %19, %21
  %23 = phi i32 [ %11, %21 ], [ %20, %19 ]
  br label %24

24:                                               ; preds = %22, %10
  %25 = phi i1 [ %18, %22 ], [ false, %10 ]
  %26 = phi i32 [ %23, %22 ], [ %11, %10 ]
  br label %27

27:                                               ; preds = %24
  %28 = phi i1 [ %25, %24 ]
  %29 = phi i32 [ %26, %24 ]
  br label %30

30:                                               ; preds = %27
  br i1 %28, label %10, label %4

31:                                               ; preds = %4
  %32 = phi i32 [ %5, %4 ]
  br label %33

33:                                               ; preds = %31
  ret i32 %32
}

define void @bplus_node_shift_keys_right(ptr %0, i32 %1) {
  %3 = icmp eq ptr %0, null
  br i1 %3, label %4, label %5

4:                                                ; preds = %2
  br label %7

5:                                                ; preds = %2
  %6 = icmp slt i32 %1, 0
  br label %7

7:                                                ; preds = %4, %5
  %8 = phi i1 [ %6, %5 ], [ true, %4 ]
  br label %9

9:                                                ; preds = %7
  %10 = xor i1 %8, true
  br i1 %10, label %11, label %89

11:                                               ; preds = %9
  %12 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  call void @acceptAddrDep(ptr %12)
  %13 = call ptr @getLocalAddr(ptr %12)
  %14 = load i32, ptr %13, align 4
  %15 = add i32 %1, 1
  %16 = add i32 %14, 1
  %17 = sext i32 %16 to i64
  %18 = sext i32 %15 to i64
  %19 = sext i32 %14 to i64
  %20 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 2
  br label %21

21:                                               ; preds = %24, %11
  %22 = phi i64 [ %34, %24 ], [ %18, %11 ]
  %23 = icmp slt i64 %22, %17
  br i1 %23, label %24, label %35

24:                                               ; preds = %21
  %25 = sub i64 %22, %18
  %26 = sub i64 %19, %25
  %27 = trunc i64 %26 to i32
  %28 = add i32 %27, -1
  %29 = getelementptr i32, ptr %20, i32 %28
  call void @acceptAddrDep(ptr %29)
  %30 = call ptr @getLocalAddr(ptr %29)
  %31 = load i32, ptr %30, align 4
  %32 = getelementptr i32, ptr %20, i32 %27
  %33 = call ptr @getLocalAddr(ptr %32)
  store i32 %31, ptr %33, align 4
  %34 = add i64 %22, 1
  br label %21

35:                                               ; preds = %21
  call void @acceptAddrDep(ptr %0)
  %36 = call ptr @getLocalAddr(ptr %0)
  %37 = load i32, ptr %36, align 4
  %38 = icmp eq i32 %37, 1
  br i1 %38, label %39, label %64

39:                                               ; preds = %35
  call void @acceptAddrDep(ptr %12)
  %40 = call ptr @getLocalAddr(ptr %12)
  %41 = load i32, ptr %40, align 4
  %42 = add i32 %41, 1
  %43 = sext i32 %42 to i64
  %44 = sext i32 %41 to i64
  %45 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 4
  br label %46

46:                                               ; preds = %49, %39
  %47 = phi i64 [ %62, %49 ], [ %18, %39 ]
  %48 = icmp slt i64 %47, %43
  br i1 %48, label %49, label %63

49:                                               ; preds = %46
  %50 = sub i64 %47, %18
  %51 = sub i64 %44, %50
  %52 = trunc i64 %51 to i32
  call void @acceptAddrDep(ptr %45)
  %53 = call ptr @getLocalAddr(ptr %45)
  %54 = load ptr, ptr %53, align 8
  %55 = add i32 %52, -1
  %56 = sext i32 %55 to i64
  %57 = getelementptr i32, ptr %54, i64 %56
  call void @acceptAddrDep(ptr %57)
  %58 = call ptr @getLocalAddr(ptr %57)
  %59 = load i32, ptr %58, align 4
  %60 = getelementptr i32, ptr %54, i64 %51
  %61 = call ptr @getLocalAddr(ptr %60)
  store i32 %59, ptr %61, align 4
  %62 = add i64 %47, 1
  br label %46

63:                                               ; preds = %46, %74
  br label %88

64:                                               ; preds = %35
  call void @acceptAddrDep(ptr %12)
  %65 = call ptr @getLocalAddr(ptr %12)
  %66 = load i32, ptr %65, align 4
  %67 = add i32 %66, 1
  %68 = add i32 %1, 2
  %69 = add i32 %66, 2
  %70 = sext i32 %69 to i64
  %71 = sext i32 %68 to i64
  %72 = sext i32 %67 to i64
  %73 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 3
  br label %74

74:                                               ; preds = %77, %64
  %75 = phi i64 [ %87, %77 ], [ %71, %64 ]
  %76 = icmp slt i64 %75, %70
  br i1 %76, label %77, label %63

77:                                               ; preds = %74
  %78 = sub i64 %75, %71
  %79 = sub i64 %72, %78
  %80 = trunc i64 %79 to i32
  %81 = add i32 %80, -1
  %82 = getelementptr ptr, ptr %73, i32 %81
  call void @acceptAddrDep(ptr %82)
  %83 = call ptr @getLocalAddr(ptr %82)
  %84 = load ptr, ptr %83, align 8
  %85 = getelementptr ptr, ptr %73, i32 %80
  call void @addAddrDep(ptr %85, ptr %84)
  %86 = call ptr @getLocalAddr(ptr %85)
  store ptr %84, ptr %86, align 8
  %87 = add i64 %75, 1
  br label %74

88:                                               ; preds = %63
  br label %89

89:                                               ; preds = %88, %9
  ret void
}

define ptr @bplus_tree_find_leaf(ptr %0, i32 %1) {
  %3 = icmp eq ptr %0, null
  br i1 %3, label %4, label %5

4:                                                ; preds = %2
  br label %9

5:                                                ; preds = %2
  call void @acceptAddrDep(ptr %0)
  %6 = call ptr @getLocalAddr(ptr %0)
  %7 = load ptr, ptr %6, align 8
  %8 = icmp eq ptr %7, null
  br label %9

9:                                                ; preds = %4, %5
  %10 = phi i1 [ %8, %5 ], [ true, %4 ]
  br label %11

11:                                               ; preds = %9
  br i1 %10, label %12, label %14

12:                                               ; preds = %17, %11
  %13 = phi ptr [ %18, %17 ], [ null, %11 ]
  br label %49

14:                                               ; preds = %11
  call void @acceptAddrDep(ptr %0)
  %15 = call ptr @getLocalAddr(ptr %0)
  %16 = load ptr, ptr %15, align 8
  br label %17

17:                                               ; preds = %44, %14
  %18 = phi ptr [ %48, %44 ], [ %16, %14 ]
  call void @acceptAddrDep(ptr %18)
  %19 = call ptr @getLocalAddr(ptr %18)
  %20 = load i32, ptr %19, align 4
  %21 = icmp eq i32 %20, 0
  br i1 %21, label %22, label %12

22:                                               ; preds = %17
  %23 = phi ptr [ %18, %17 ]
  %24 = call i32 @bplus_node_find_key_index(ptr %23, i32 %1)
  %25 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %23, i32 0, i32 1
  call void @acceptAddrDep(ptr %25)
  %26 = call ptr @getLocalAddr(ptr %25)
  %27 = load i32, ptr %26, align 4
  %28 = icmp slt i32 %24, %27
  br i1 %28, label %29, label %40

29:                                               ; preds = %22
  %30 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %23, i32 0, i32 2
  %31 = getelementptr i32, ptr %30, i32 %24
  call void @acceptAddrDep(ptr %31)
  %32 = call ptr @getLocalAddr(ptr %31)
  %33 = load i32, ptr %32, align 4
  %34 = icmp sge i32 %1, %33
  br i1 %34, label %35, label %37

35:                                               ; preds = %29
  %36 = add i32 %24, 1
  br label %38

37:                                               ; preds = %29
  br label %38

38:                                               ; preds = %35, %37
  %39 = phi i32 [ %24, %37 ], [ %36, %35 ]
  br label %40

40:                                               ; preds = %38, %22
  %41 = phi i32 [ %39, %38 ], [ %24, %22 ]
  br label %42

42:                                               ; preds = %40
  %43 = phi i32 [ %41, %40 ]
  br label %44

44:                                               ; preds = %42
  %45 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %23, i32 0, i32 3
  %46 = getelementptr ptr, ptr %45, i32 %43
  call void @acceptAddrDep(ptr %46)
  %47 = call ptr @getLocalAddr(ptr %46)
  %48 = load ptr, ptr %47, align 8
  br label %17

49:                                               ; preds = %12
  %50 = phi ptr [ %13, %12 ]
  br label %51

51:                                               ; preds = %49
  ret ptr %50
}

define void @bplus_node_insert_key_value(ptr %0, i32 %1, i32 %2) {
  %4 = icmp eq ptr %0, null
  br i1 %4, label %5, label %6

5:                                                ; preds = %3
  br label %10

6:                                                ; preds = %3
  call void @acceptAddrDep(ptr %0)
  %7 = call ptr @getLocalAddr(ptr %0)
  %8 = load i32, ptr %7, align 4
  %9 = icmp ne i32 %8, 1
  br label %10

10:                                               ; preds = %5, %6
  %11 = phi i1 [ %9, %6 ], [ true, %5 ]
  br label %12

12:                                               ; preds = %10
  br i1 %11, label %13, label %14

13:                                               ; preds = %39, %38, %12
  br label %53

14:                                               ; preds = %12
  %15 = call i32 @bplus_node_find_key_index(ptr %0, i32 %1)
  %16 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  call void @acceptAddrDep(ptr %16)
  %17 = call ptr @getLocalAddr(ptr %16)
  %18 = load i32, ptr %17, align 4
  %19 = icmp slt i32 %15, %18
  br i1 %19, label %20, label %34

20:                                               ; preds = %14
  %21 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 2
  %22 = getelementptr i32, ptr %21, i32 %15
  call void @acceptAddrDep(ptr %22)
  %23 = call ptr @getLocalAddr(ptr %22)
  %24 = load i32, ptr %23, align 4
  %25 = icmp eq i32 %24, %1
  %26 = icmp ne i32 %24, %1
  br i1 %25, label %27, label %34

27:                                               ; preds = %20
  %28 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 4
  call void @acceptAddrDep(ptr %28)
  %29 = call ptr @getLocalAddr(ptr %28)
  %30 = load ptr, ptr %29, align 8
  %31 = sext i32 %15 to i64
  %32 = getelementptr i32, ptr %30, i64 %31
  %33 = call ptr @getLocalAddr(ptr %32)
  store i32 %2, ptr %33, align 4
  br label %34

34:                                               ; preds = %27, %20, %14
  %35 = phi i1 [ %26, %27 ], [ %26, %20 ], [ true, %14 ]
  br label %36

36:                                               ; preds = %34
  %37 = phi i1 [ %35, %34 ]
  br label %38

38:                                               ; preds = %36
  br i1 %37, label %39, label %13

39:                                               ; preds = %38
  call void @bplus_node_shift_keys_right(ptr %0, i32 %15)
  %40 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 2
  %41 = getelementptr i32, ptr %40, i32 %15
  %42 = call ptr @getLocalAddr(ptr %41)
  store i32 %1, ptr %42, align 4
  %43 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 4
  call void @acceptAddrDep(ptr %43)
  %44 = call ptr @getLocalAddr(ptr %43)
  %45 = load ptr, ptr %44, align 8
  %46 = sext i32 %15 to i64
  %47 = getelementptr i32, ptr %45, i64 %46
  %48 = call ptr @getLocalAddr(ptr %47)
  store i32 %2, ptr %48, align 4
  call void @acceptAddrDep(ptr %16)
  %49 = call ptr @getLocalAddr(ptr %16)
  %50 = load i32, ptr %49, align 4
  %51 = add i32 %50, 1
  %52 = call ptr @getLocalAddr(ptr %16)
  store i32 %51, ptr %52, align 4
  br label %13

53:                                               ; preds = %13
  ret void
}

define void @bplus_node_insert_key_child(ptr %0, i32 %1, ptr %2) {
  %4 = icmp eq ptr %0, null
  br i1 %4, label %5, label %6

5:                                                ; preds = %3
  br label %10

6:                                                ; preds = %3
  call void @acceptAddrDep(ptr %0)
  %7 = call ptr @getLocalAddr(ptr %0)
  %8 = load i32, ptr %7, align 4
  %9 = icmp ne i32 %8, 0
  br label %10

10:                                               ; preds = %5, %6
  %11 = phi i1 [ %9, %6 ], [ true, %5 ]
  br label %12

12:                                               ; preds = %10
  %13 = xor i1 %11, true
  br i1 %13, label %14, label %33

14:                                               ; preds = %12
  %15 = call i32 @bplus_node_find_key_index(ptr %0, i32 %1)
  call void @bplus_node_shift_keys_right(ptr %0, i32 %15)
  %16 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 2
  %17 = getelementptr i32, ptr %16, i32 %15
  %18 = call ptr @getLocalAddr(ptr %17)
  store i32 %1, ptr %18, align 4
  %19 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 3
  %20 = add i32 %15, 1
  %21 = getelementptr ptr, ptr %19, i32 %20
  call void @addAddrDep(ptr %21, ptr %2)
  %22 = call ptr @getLocalAddr(ptr %21)
  store ptr %2, ptr %22, align 8
  %23 = icmp ne ptr %2, null
  br i1 %23, label %24, label %27

24:                                               ; preds = %14
  %25 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %2, i32 0, i32 6
  call void @addAddrDep(ptr %25, ptr %0)
  %26 = call ptr @getLocalAddr(ptr %25)
  store ptr %0, ptr %26, align 8
  br label %27

27:                                               ; preds = %24, %14
  %28 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  call void @acceptAddrDep(ptr %28)
  %29 = call ptr @getLocalAddr(ptr %28)
  %30 = load i32, ptr %29, align 4
  %31 = add i32 %30, 1
  %32 = call ptr @getLocalAddr(ptr %28)
  store i32 %31, ptr %32, align 4
  br label %33

33:                                               ; preds = %27, %12
  ret void
}

define ptr @bplus_node_split_leaf(ptr %0) {
  %2 = icmp eq ptr %0, null
  br i1 %2, label %3, label %4

3:                                                ; preds = %1
  br label %8

4:                                                ; preds = %1
  call void @acceptAddrDep(ptr %0)
  %5 = call ptr @getLocalAddr(ptr %0)
  %6 = load i32, ptr %5, align 4
  %7 = icmp ne i32 %6, 1
  br label %8

8:                                                ; preds = %3, %4
  %9 = phi i1 [ %7, %4 ], [ true, %3 ]
  br label %10

10:                                               ; preds = %8
  br i1 %9, label %11, label %13

11:                                               ; preds = %66, %10
  %12 = phi ptr [ %67, %66 ], [ null, %10 ]
  br label %68

13:                                               ; preds = %10
  %14 = call ptr @bplus_node_create(i32 1)
  %15 = icmp eq ptr %14, null
  br i1 %15, label %16, label %17

16:                                               ; preds = %13
  br label %66

17:                                               ; preds = %13
  %18 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %14, i32 0, i32 1
  %19 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  call void @acceptAddrDep(ptr %19)
  %20 = call ptr @getLocalAddr(ptr %19)
  %21 = load i32, ptr %20, align 4
  %22 = add i32 %21, -166
  %23 = call ptr @getLocalAddr(ptr %18)
  store i32 %22, ptr %23, align 4
  %24 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %14, i32 0, i32 2
  %25 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 2
  %26 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %14, i32 0, i32 4
  %27 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 4
  br label %28

28:                                               ; preds = %33, %17
  %29 = phi i32 [ %52, %33 ], [ 0, %17 ]
  call void @acceptAddrDep(ptr %18)
  %30 = call ptr @getLocalAddr(ptr %18)
  %31 = load i32, ptr %30, align 4
  %32 = icmp slt i32 %29, %31
  br i1 %32, label %33, label %53

33:                                               ; preds = %28
  %34 = phi i32 [ %29, %28 ]
  %35 = add i32 %34, 166
  %36 = getelementptr i32, ptr %25, i32 %35
  call void @acceptAddrDep(ptr %36)
  %37 = call ptr @getLocalAddr(ptr %36)
  %38 = load i32, ptr %37, align 4
  %39 = getelementptr i32, ptr %24, i32 %34
  %40 = call ptr @getLocalAddr(ptr %39)
  store i32 %38, ptr %40, align 4
  call void @acceptAddrDep(ptr %26)
  %41 = call ptr @getLocalAddr(ptr %26)
  %42 = load ptr, ptr %41, align 8
  %43 = sext i32 %34 to i64
  call void @acceptAddrDep(ptr %27)
  %44 = call ptr @getLocalAddr(ptr %27)
  %45 = load ptr, ptr %44, align 8
  %46 = sext i32 %35 to i64
  %47 = getelementptr i32, ptr %45, i64 %46
  call void @acceptAddrDep(ptr %47)
  %48 = call ptr @getLocalAddr(ptr %47)
  %49 = load i32, ptr %48, align 4
  %50 = getelementptr i32, ptr %42, i64 %43
  %51 = call ptr @getLocalAddr(ptr %50)
  store i32 %49, ptr %51, align 4
  %52 = add i32 %34, 1
  br label %28

53:                                               ; preds = %28
  %54 = call ptr @getLocalAddr(ptr %19)
  store i32 166, ptr %54, align 4
  %55 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %14, i32 0, i32 5
  %56 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 5
  call void @acceptAddrDep(ptr %56)
  %57 = call ptr @getLocalAddr(ptr %56)
  %58 = load ptr, ptr %57, align 8
  call void @addAddrDep(ptr %55, ptr %58)
  %59 = call ptr @getLocalAddr(ptr %55)
  store ptr %58, ptr %59, align 8
  call void @addAddrDep(ptr %56, ptr %14)
  %60 = call ptr @getLocalAddr(ptr %56)
  store ptr %14, ptr %60, align 8
  %61 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %14, i32 0, i32 6
  %62 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 6
  call void @acceptAddrDep(ptr %62)
  %63 = call ptr @getLocalAddr(ptr %62)
  %64 = load ptr, ptr %63, align 8
  call void @addAddrDep(ptr %61, ptr %64)
  %65 = call ptr @getLocalAddr(ptr %61)
  store ptr %64, ptr %65, align 8
  br label %66

66:                                               ; preds = %16, %53
  %67 = phi ptr [ %14, %53 ], [ null, %16 ]
  br label %11

68:                                               ; preds = %11
  %69 = phi ptr [ %12, %11 ]
  br label %70

70:                                               ; preds = %68
  ret ptr %69
}

define ptr @bplus_node_split_internal(ptr %0) {
  %2 = icmp eq ptr %0, null
  br i1 %2, label %3, label %4

3:                                                ; preds = %1
  br label %8

4:                                                ; preds = %1
  call void @acceptAddrDep(ptr %0)
  %5 = call ptr @getLocalAddr(ptr %0)
  %6 = load i32, ptr %5, align 4
  %7 = icmp ne i32 %6, 0
  br label %8

8:                                                ; preds = %3, %4
  %9 = phi i1 [ %7, %4 ], [ true, %3 ]
  br label %10

10:                                               ; preds = %8
  br i1 %9, label %11, label %13

11:                                               ; preds = %73, %10
  %12 = phi ptr [ %74, %73 ], [ null, %10 ]
  br label %75

13:                                               ; preds = %10
  %14 = call ptr @bplus_node_create(i32 0)
  %15 = icmp eq ptr %14, null
  br i1 %15, label %16, label %17

16:                                               ; preds = %13
  br label %73

17:                                               ; preds = %13
  %18 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %14, i32 0, i32 1
  %19 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  call void @acceptAddrDep(ptr %19)
  %20 = call ptr @getLocalAddr(ptr %19)
  %21 = load i32, ptr %20, align 4
  %22 = add i32 %21, -167
  %23 = call ptr @getLocalAddr(ptr %18)
  store i32 %22, ptr %23, align 4
  %24 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %14, i32 0, i32 2
  %25 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 2
  br label %26

26:                                               ; preds = %31, %17
  %27 = phi i32 [ %39, %31 ], [ 0, %17 ]
  call void @acceptAddrDep(ptr %18)
  %28 = call ptr @getLocalAddr(ptr %18)
  %29 = load i32, ptr %28, align 4
  %30 = icmp slt i32 %27, %29
  br i1 %30, label %31, label %40

31:                                               ; preds = %26
  %32 = phi i32 [ %27, %26 ]
  %33 = add i32 %32, 167
  %34 = getelementptr i32, ptr %25, i32 %33
  call void @acceptAddrDep(ptr %34)
  %35 = call ptr @getLocalAddr(ptr %34)
  %36 = load i32, ptr %35, align 4
  %37 = getelementptr i32, ptr %24, i32 %32
  %38 = call ptr @getLocalAddr(ptr %37)
  store i32 %36, ptr %38, align 4
  %39 = add i32 %32, 1
  br label %26

40:                                               ; preds = %26
  %41 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %14, i32 0, i32 3
  %42 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 3
  br label %43

43:                                               ; preds = %64, %40
  %44 = phi i32 [ %65, %64 ], [ 0, %40 ]
  call void @acceptAddrDep(ptr %18)
  %45 = call ptr @getLocalAddr(ptr %18)
  %46 = load i32, ptr %45, align 4
  %47 = icmp sle i32 %44, %46
  br i1 %47, label %48, label %66

48:                                               ; preds = %43
  %49 = phi i32 [ %44, %43 ]
  %50 = add i32 %49, 167
  %51 = getelementptr ptr, ptr %42, i32 %50
  call void @acceptAddrDep(ptr %51)
  %52 = call ptr @getLocalAddr(ptr %51)
  %53 = load ptr, ptr %52, align 8
  %54 = getelementptr ptr, ptr %41, i32 %49
  call void @addAddrDep(ptr %54, ptr %53)
  %55 = call ptr @getLocalAddr(ptr %54)
  store ptr %53, ptr %55, align 8
  call void @acceptAddrDep(ptr %54)
  %56 = call ptr @getLocalAddr(ptr %54)
  %57 = load ptr, ptr %56, align 8
  %58 = icmp ne ptr %57, null
  br i1 %58, label %59, label %64

59:                                               ; preds = %48
  call void @acceptAddrDep(ptr %54)
  %60 = call ptr @getLocalAddr(ptr %54)
  %61 = load ptr, ptr %60, align 8
  %62 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %61, i32 0, i32 6
  call void @addAddrDep(ptr %62, ptr %14)
  %63 = call ptr @getLocalAddr(ptr %62)
  store ptr %14, ptr %63, align 8
  br label %64

64:                                               ; preds = %59, %48
  %65 = add i32 %49, 1
  br label %43

66:                                               ; preds = %43
  %67 = call ptr @getLocalAddr(ptr %19)
  store i32 166, ptr %67, align 4
  %68 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %14, i32 0, i32 6
  %69 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 6
  call void @acceptAddrDep(ptr %69)
  %70 = call ptr @getLocalAddr(ptr %69)
  %71 = load ptr, ptr %70, align 8
  call void @addAddrDep(ptr %68, ptr %71)
  %72 = call ptr @getLocalAddr(ptr %68)
  store ptr %71, ptr %72, align 8
  br label %73

73:                                               ; preds = %16, %66
  %74 = phi ptr [ %14, %66 ], [ null, %16 ]
  br label %11

75:                                               ; preds = %11
  %76 = phi ptr [ %12, %11 ]
  br label %77

77:                                               ; preds = %75
  ret ptr %76
}

define void @bplus_tree_insert_into_parent(ptr %0, ptr %1, i32 %2, ptr %3) {
  %5 = icmp eq ptr %0, null
  br i1 %5, label %6, label %7

6:                                                ; preds = %4
  br label %9

7:                                                ; preds = %4
  %8 = icmp eq ptr %1, null
  br label %9

9:                                                ; preds = %6, %7
  %10 = phi i1 [ %8, %7 ], [ true, %6 ]
  br label %11

11:                                               ; preds = %9
  br i1 %10, label %12, label %13

12:                                               ; preds = %11
  br label %15

13:                                               ; preds = %11
  %14 = icmp eq ptr %3, null
  br label %15

15:                                               ; preds = %12, %13
  %16 = phi i1 [ %14, %13 ], [ true, %12 ]
  br label %17

17:                                               ; preds = %15
  br i1 %16, label %18, label %19

18:                                               ; preds = %46, %17
  br label %57

19:                                               ; preds = %17
  %20 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %1, i32 0, i32 6
  call void @acceptAddrDep(ptr %20)
  %21 = call ptr @getLocalAddr(ptr %20)
  %22 = load ptr, ptr %21, align 8
  %23 = icmp eq ptr %22, null
  br i1 %23, label %24, label %47

24:                                               ; preds = %19
  %25 = call ptr @bplus_node_create(i32 0)
  %26 = icmp eq ptr %25, null
  %27 = xor i1 %26, true
  br i1 %27, label %28, label %46

28:                                               ; preds = %24
  %29 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %25, i32 0, i32 2
  %30 = call ptr @getLocalAddr(ptr %29)
  store i32 %2, ptr %30, align 4
  %31 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %25, i32 0, i32 3
  call void @addAddrDep(ptr %31, ptr %1)
  %32 = call ptr @getLocalAddr(ptr %31)
  store ptr %1, ptr %32, align 8
  %33 = getelementptr ptr, ptr %31, i32 1
  call void @addAddrDep(ptr %33, ptr %3)
  %34 = call ptr @getLocalAddr(ptr %33)
  store ptr %3, ptr %34, align 8
  %35 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %25, i32 0, i32 1
  %36 = call ptr @getLocalAddr(ptr %35)
  store i32 1, ptr %36, align 4
  call void @addAddrDep(ptr %20, ptr %25)
  %37 = call ptr @getLocalAddr(ptr %20)
  store ptr %25, ptr %37, align 8
  %38 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %3, i32 0, i32 6
  call void @addAddrDep(ptr %38, ptr %25)
  %39 = call ptr @getLocalAddr(ptr %38)
  store ptr %25, ptr %39, align 8
  call void @addAddrDep(ptr %0, ptr %25)
  %40 = call ptr @getLocalAddr(ptr %0)
  store ptr %25, ptr %40, align 8
  %41 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  call void @acceptAddrDep(ptr %41)
  %42 = call ptr @getLocalAddr(ptr %41)
  %43 = load i32, ptr %42, align 4
  %44 = add i32 %43, 1
  %45 = call ptr @getLocalAddr(ptr %41)
  store i32 %44, ptr %45, align 4
  br label %46

46:                                               ; preds = %28, %24, %50, %56
  br label %18

47:                                               ; preds = %19
  %48 = call i8 @bplus_node_is_full(ptr %22)
  %49 = icmp ne i8 %48, 0
  br i1 %49, label %50, label %56

50:                                               ; preds = %47
  call void @bplus_node_insert_key_child(ptr %22, i32 %2, ptr %3)
  %51 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %22, i32 0, i32 2
  %52 = getelementptr i32, ptr %51, i32 166
  call void @acceptAddrDep(ptr %52)
  %53 = call ptr @getLocalAddr(ptr %52)
  %54 = load i32, ptr %53, align 4
  %55 = call ptr @bplus_node_split_internal(ptr %22)
  call void @bplus_tree_insert_into_parent(ptr %0, ptr %22, i32 %54, ptr %55)
  br label %46

56:                                               ; preds = %47
  call void @bplus_node_insert_key_child(ptr %22, i32 %2, ptr %3)
  br label %46

57:                                               ; preds = %18
  ret void
}

define i8 @bplus_tree_search(ptr %0, i32 %1, ptr %2) {
  %4 = icmp eq ptr %0, null
  br i1 %4, label %5, label %6

5:                                                ; preds = %3
  br label %10

6:                                                ; preds = %3
  call void @acceptAddrDep(ptr %0)
  %7 = call ptr @getLocalAddr(ptr %0)
  %8 = load ptr, ptr %7, align 8
  %9 = icmp eq ptr %8, null
  br label %10

10:                                               ; preds = %5, %6
  %11 = phi i1 [ %9, %6 ], [ true, %5 ]
  br label %12

12:                                               ; preds = %10
  %13 = select i1 %11, i8 0, i8 undef
  br i1 %11, label %14, label %17

14:                                               ; preds = %54, %12
  %15 = phi i1 [ %55, %54 ], [ false, %12 ]
  %16 = phi i8 [ %56, %54 ], [ %13, %12 ]
  br label %57

17:                                               ; preds = %12
  %18 = call ptr @bplus_tree_find_leaf(ptr %0, i32 %1)
  %19 = icmp eq ptr %18, null
  br i1 %19, label %20, label %23

20:                                               ; preds = %51, %17
  %21 = phi i1 [ %52, %51 ], [ false, %17 ]
  %22 = phi i8 [ %53, %51 ], [ %13, %17 ]
  br label %54

23:                                               ; preds = %17
  %24 = call i32 @bplus_node_find_key_index(ptr %18, i32 %1)
  %25 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %18, i32 0, i32 1
  call void @acceptAddrDep(ptr %25)
  %26 = call ptr @getLocalAddr(ptr %25)
  %27 = load i32, ptr %26, align 4
  %28 = icmp slt i32 %24, %27
  br i1 %28, label %29, label %48

29:                                               ; preds = %23
  %30 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %18, i32 0, i32 2
  %31 = getelementptr i32, ptr %30, i32 %24
  call void @acceptAddrDep(ptr %31)
  %32 = call ptr @getLocalAddr(ptr %31)
  %33 = load i32, ptr %32, align 4
  %34 = icmp eq i32 %33, %1
  %35 = icmp ne ptr %2, null
  %36 = and i1 %34, %35
  %37 = icmp ne i32 %33, %1
  %38 = select i1 %34, i8 1, i8 %13
  br i1 %36, label %39, label %48

39:                                               ; preds = %29
  %40 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %18, i32 0, i32 4
  call void @acceptAddrDep(ptr %40)
  %41 = call ptr @getLocalAddr(ptr %40)
  %42 = load ptr, ptr %41, align 8
  %43 = sext i32 %24 to i64
  %44 = getelementptr i32, ptr %42, i64 %43
  call void @acceptAddrDep(ptr %44)
  %45 = call ptr @getLocalAddr(ptr %44)
  %46 = load i32, ptr %45, align 4
  %47 = call ptr @getLocalAddr(ptr %2)
  store i32 %46, ptr %47, align 4
  br label %48

48:                                               ; preds = %39, %29, %23
  %49 = phi i1 [ %37, %39 ], [ %37, %29 ], [ true, %23 ]
  %50 = phi i8 [ %38, %39 ], [ %38, %29 ], [ %13, %23 ]
  br label %51

51:                                               ; preds = %48
  %52 = phi i1 [ %49, %48 ]
  %53 = phi i8 [ %50, %48 ]
  br label %20

54:                                               ; preds = %20
  %55 = phi i1 [ %21, %20 ]
  %56 = phi i8 [ %22, %20 ]
  br label %14

57:                                               ; preds = %14
  %58 = phi i1 [ %15, %14 ]
  %59 = phi i8 [ %16, %14 ]
  br label %60

60:                                               ; preds = %57
  %61 = select i1 %58, i8 0, i8 %59
  ret i8 %61
}

define i8 @bplus_tree_insert(ptr %0, i32 %1, i32 %2) {
  %4 = alloca ptr, i64 1, align 8
  %5 = alloca ptr, i64 1, align 8
  %6 = icmp eq ptr %0, null
  %7 = select i1 %6, i8 0, i8 undef
  br i1 %6, label %8, label %11

8:                                                ; preds = %73, %3
  %9 = phi i1 [ %74, %73 ], [ false, %3 ]
  %10 = phi i8 [ %75, %73 ], [ %7, %3 ]
  br label %76

11:                                               ; preds = %3
  call void @acceptAddrDep(ptr %0)
  %12 = call ptr @getLocalAddr(ptr %0)
  %13 = load ptr, ptr %12, align 8
  %14 = icmp eq ptr %13, null
  br i1 %14, label %15, label %44

15:                                               ; preds = %11
  %16 = call ptr @bplus_node_create(i32 1)
  call void @addAddrDep(ptr %0, ptr %16)
  %17 = call ptr @getLocalAddr(ptr %0)
  store ptr %16, ptr %17, align 8
  call void @acceptAddrDep(ptr %0)
  %18 = call ptr @getLocalAddr(ptr %0)
  %19 = load ptr, ptr %18, align 8
  %20 = icmp eq ptr %19, null
  %21 = xor i1 %20, true
  br i1 %21, label %22, label %41

22:                                               ; preds = %15
  call void @acceptAddrDep(ptr %0)
  %23 = call ptr @getLocalAddr(ptr %0)
  %24 = load ptr, ptr %23, align 8
  %25 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %24, i32 0, i32 2
  %26 = call ptr @getLocalAddr(ptr %25)
  store i32 %1, ptr %26, align 4
  call void @acceptAddrDep(ptr %0)
  %27 = call ptr @getLocalAddr(ptr %0)
  %28 = load ptr, ptr %27, align 8
  %29 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %28, i32 0, i32 4
  call void @acceptAddrDep(ptr %29)
  %30 = call ptr @getLocalAddr(ptr %29)
  %31 = load ptr, ptr %30, align 8
  %32 = call ptr @getLocalAddr(ptr %31)
  store i32 %2, ptr %32, align 4
  call void @acceptAddrDep(ptr %0)
  %33 = call ptr @getLocalAddr(ptr %0)
  %34 = load ptr, ptr %33, align 8
  %35 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %34, i32 0, i32 1
  %36 = call ptr @getLocalAddr(ptr %35)
  store i32 1, ptr %36, align 4
  %37 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  %38 = call ptr @getLocalAddr(ptr %37)
  store i32 1, ptr %38, align 4
  %39 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 2
  %40 = call ptr @getLocalAddr(ptr %39)
  store i32 1, ptr %40, align 4
  br label %41

41:                                               ; preds = %22, %15, %70
  %42 = phi i1 [ %71, %70 ], [ false, %22 ], [ false, %15 ]
  %43 = phi i8 [ %72, %70 ], [ %7, %22 ], [ %7, %15 ]
  br label %73

44:                                               ; preds = %11
  %45 = call ptr @bplus_tree_find_leaf(ptr %0, i32 %1)
  %46 = getelementptr ptr, ptr %5, i32 0
  call void @addAddrDep(ptr %46, ptr %45)
  %47 = call ptr @getLocalAddr(ptr %46)
  store ptr %45, ptr %47, align 8
  %48 = icmp eq ptr %45, null
  br i1 %48, label %49, label %52

49:                                               ; preds = %67, %44
  %50 = phi i1 [ %68, %67 ], [ false, %44 ]
  %51 = phi i8 [ %69, %67 ], [ %7, %44 ]
  br label %70

52:                                               ; preds = %44
  %53 = call i8 @bplus_node_is_full(ptr %45)
  %54 = icmp eq i8 %53, 0
  %55 = icmp ne i8 %53, 0
  %56 = select i1 %54, i8 1, i8 %7
  br i1 %54, label %57, label %58

57:                                               ; preds = %52
  call void @bplus_node_insert_key_value(ptr %45, i32 %1, i32 %2)
  br label %58

58:                                               ; preds = %57, %52
  br i1 %55, label %59, label %66

59:                                               ; preds = %58
  call void @bplus_node_insert_key_value(ptr %45, i32 %1, i32 %2)
  %60 = call ptr @bplus_node_split_leaf(ptr %45)
  %61 = getelementptr ptr, ptr %4, i32 0
  call void @addAddrDep(ptr %61, ptr %60)
  %62 = call ptr @getLocalAddr(ptr %61)
  store ptr %60, ptr %62, align 8
  %63 = icmp eq ptr %60, null
  %64 = xor i1 %63, true
  %65 = select i1 %63, i8 0, i8 %56
  br label %67

66:                                               ; preds = %58
  br label %67

67:                                               ; preds = %59, %66
  %68 = phi i1 [ false, %66 ], [ %64, %59 ]
  %69 = phi i8 [ %7, %66 ], [ %65, %59 ]
  br label %49

70:                                               ; preds = %49
  %71 = phi i1 [ %50, %49 ]
  %72 = phi i8 [ %51, %49 ]
  br label %41

73:                                               ; preds = %41
  %74 = phi i1 [ %42, %41 ]
  %75 = phi i8 [ %43, %41 ]
  br label %8

76:                                               ; preds = %8
  %77 = phi i1 [ %9, %8 ]
  %78 = phi i8 [ %10, %8 ]
  br label %79

79:                                               ; preds = %76
  %80 = select i1 %77, i8 1, i8 %78
  br i1 %77, label %81, label %96

81:                                               ; preds = %79
  %82 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 2
  call void @acceptAddrDep(ptr %82)
  %83 = call ptr @getLocalAddr(ptr %82)
  %84 = load i32, ptr %83, align 4
  %85 = add i32 %84, 1
  %86 = call ptr @getLocalAddr(ptr %82)
  store i32 %85, ptr %86, align 4
  %87 = getelementptr ptr, ptr %4, i32 0
  call void @acceptAddrDep(ptr %87)
  %88 = call ptr @getLocalAddr(ptr %87)
  %89 = load ptr, ptr %88, align 8
  %90 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %89, i32 0, i32 2
  call void @acceptAddrDep(ptr %90)
  %91 = call ptr @getLocalAddr(ptr %90)
  %92 = load i32, ptr %91, align 4
  %93 = getelementptr ptr, ptr %5, i32 0
  call void @acceptAddrDep(ptr %93)
  %94 = call ptr @getLocalAddr(ptr %93)
  %95 = load ptr, ptr %94, align 8
  call void @bplus_tree_insert_into_parent(ptr %0, ptr %95, i32 %92, ptr %89)
  br label %96

96:                                               ; preds = %81, %79
  ret i8 %80
}

define void @bplus_tree_traverse_leaves(ptr %0) {
  %2 = icmp eq ptr %0, null
  br i1 %2, label %3, label %4

3:                                                ; preds = %1
  br label %8

4:                                                ; preds = %1
  call void @acceptAddrDep(ptr %0)
  %5 = call ptr @getLocalAddr(ptr %0)
  %6 = load ptr, ptr %5, align 8
  %7 = icmp eq ptr %6, null
  br label %8

8:                                                ; preds = %3, %4
  %9 = phi i1 [ %7, %4 ], [ true, %3 ]
  br label %10

10:                                               ; preds = %8
  br i1 %9, label %11, label %13

11:                                               ; preds = %10
  %12 = call i32 (ptr, ...) @printf(ptr @str4)
  br label %60

13:                                               ; preds = %10
  call void @acceptAddrDep(ptr %0)
  %14 = call ptr @getLocalAddr(ptr %0)
  %15 = load ptr, ptr %14, align 8
  br label %16

16:                                               ; preds = %21, %13
  %17 = phi ptr [ %25, %21 ], [ %15, %13 ]
  call void @acceptAddrDep(ptr %17)
  %18 = call ptr @getLocalAddr(ptr %17)
  %19 = load i32, ptr %18, align 4
  %20 = icmp eq i32 %19, 0
  br i1 %20, label %21, label %26

21:                                               ; preds = %16
  %22 = phi ptr [ %17, %16 ]
  %23 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %22, i32 0, i32 3
  call void @acceptAddrDep(ptr %23)
  %24 = call ptr @getLocalAddr(ptr %23)
  %25 = load ptr, ptr %24, align 8
  br label %16

26:                                               ; preds = %16
  %27 = call i32 (ptr, ...) @printf(ptr @str5)
  br label %28

28:                                               ; preds = %54, %26
  %29 = phi ptr [ %57, %54 ], [ %17, %26 ]
  %30 = icmp ne ptr %29, null
  br i1 %30, label %31, label %58

31:                                               ; preds = %28
  %32 = phi ptr [ %29, %28 ]
  %33 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %32, i32 0, i32 1
  %34 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %32, i32 0, i32 2
  %35 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %32, i32 0, i32 4
  br label %36

36:                                               ; preds = %41, %31
  %37 = phi i32 [ %53, %41 ], [ 0, %31 ]
  call void @acceptAddrDep(ptr %33)
  %38 = call ptr @getLocalAddr(ptr %33)
  %39 = load i32, ptr %38, align 4
  %40 = icmp slt i32 %37, %39
  br i1 %40, label %41, label %54

41:                                               ; preds = %36
  %42 = phi i32 [ %37, %36 ]
  %43 = sext i32 %42 to i64
  %44 = getelementptr i32, ptr %34, i32 %42
  call void @acceptAddrDep(ptr %44)
  %45 = call ptr @getLocalAddr(ptr %44)
  %46 = load i32, ptr %45, align 4
  call void @acceptAddrDep(ptr %35)
  %47 = call ptr @getLocalAddr(ptr %35)
  %48 = load ptr, ptr %47, align 8
  %49 = getelementptr i32, ptr %48, i64 %43
  call void @acceptAddrDep(ptr %49)
  %50 = call ptr @getLocalAddr(ptr %49)
  %51 = load i32, ptr %50, align 4
  %52 = call i32 (ptr, ...) @printf(ptr @str6, i32 %46, i32 %51)
  %53 = add i32 %42, 1
  br label %36

54:                                               ; preds = %36
  %55 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %32, i32 0, i32 5
  call void @acceptAddrDep(ptr %55)
  %56 = call ptr @getLocalAddr(ptr %55)
  %57 = load ptr, ptr %56, align 8
  br label %28

58:                                               ; preds = %28
  %59 = call i32 (ptr, ...) @printf(ptr @str7)
  br label %60

60:                                               ; preds = %11, %58
  ret void
}

define void @bplus_tree_print_tree(ptr %0, i32 %1) {
  %3 = icmp eq ptr %0, null
  %4 = xor i1 %3, true
  br i1 %4, label %5, label %78

5:                                                ; preds = %2
  %6 = sext i32 %1 to i64
  br label %7

7:                                                ; preds = %10, %5
  %8 = phi i64 [ %12, %10 ], [ 0, %5 ]
  %9 = icmp slt i64 %8, %6
  br i1 %9, label %10, label %13

10:                                               ; preds = %7
  %11 = call i32 (ptr, ...) @printf(ptr @str8)
  %12 = add i64 %8, 1
  br label %7

13:                                               ; preds = %7
  call void @acceptAddrDep(ptr %0)
  %14 = call ptr @getLocalAddr(ptr %0)
  %15 = load i32, ptr %14, align 4
  %16 = icmp eq i32 %15, 1
  br i1 %16, label %17, label %18

17:                                               ; preds = %13
  br label %19

18:                                               ; preds = %13
  br label %19

19:                                               ; preds = %17, %18
  %20 = phi ptr [ @str11, %18 ], [ @str10, %17 ]
  br label %21

21:                                               ; preds = %19
  %22 = call i32 (ptr, ...) @printf(ptr @str9, ptr %20)
  %23 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  %24 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 2
  br label %25

25:                                               ; preds = %55, %21
  %26 = phi i32 [ %56, %55 ], [ 0, %21 ]
  call void @acceptAddrDep(ptr %23)
  %27 = call ptr @getLocalAddr(ptr %23)
  %28 = load i32, ptr %27, align 4
  %29 = icmp slt i32 %26, %28
  br i1 %29, label %30, label %57

30:                                               ; preds = %25
  %31 = phi i32 [ %26, %25 ]
  %32 = getelementptr i32, ptr %24, i32 %31
  call void @acceptAddrDep(ptr %32)
  %33 = call ptr @getLocalAddr(ptr %32)
  %34 = load i32, ptr %33, align 4
  %35 = call i32 (ptr, ...) @printf(ptr @str12, i32 %34)
  call void @acceptAddrDep(ptr %0)
  %36 = call ptr @getLocalAddr(ptr %0)
  %37 = load i32, ptr %36, align 4
  %38 = icmp eq i32 %37, 1
  br i1 %38, label %39, label %48

39:                                               ; preds = %30
  %40 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 4
  call void @acceptAddrDep(ptr %40)
  %41 = call ptr @getLocalAddr(ptr %40)
  %42 = load ptr, ptr %41, align 8
  %43 = sext i32 %31 to i64
  %44 = getelementptr i32, ptr %42, i64 %43
  call void @acceptAddrDep(ptr %44)
  %45 = call ptr @getLocalAddr(ptr %44)
  %46 = load i32, ptr %45, align 4
  %47 = call i32 (ptr, ...) @printf(ptr @str13, i32 %46)
  br label %48

48:                                               ; preds = %39, %30
  call void @acceptAddrDep(ptr %23)
  %49 = call ptr @getLocalAddr(ptr %23)
  %50 = load i32, ptr %49, align 4
  %51 = add i32 %50, -1
  %52 = icmp slt i32 %31, %51
  br i1 %52, label %53, label %55

53:                                               ; preds = %48
  %54 = call i32 (ptr, ...) @printf(ptr @str14)
  br label %55

55:                                               ; preds = %53, %48
  %56 = add i32 %31, 1
  br label %25

57:                                               ; preds = %25
  %58 = call i32 (ptr, ...) @printf(ptr @str15)
  call void @acceptAddrDep(ptr %0)
  %59 = call ptr @getLocalAddr(ptr %0)
  %60 = load i32, ptr %59, align 4
  %61 = icmp eq i32 %60, 0
  br i1 %61, label %62, label %77

62:                                               ; preds = %57
  %63 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 3
  %64 = add i32 %1, 1
  br label %65

65:                                               ; preds = %70, %62
  %66 = phi i32 [ %75, %70 ], [ 0, %62 ]
  call void @acceptAddrDep(ptr %23)
  %67 = call ptr @getLocalAddr(ptr %23)
  %68 = load i32, ptr %67, align 4
  %69 = icmp sle i32 %66, %68
  br i1 %69, label %70, label %76

70:                                               ; preds = %65
  %71 = phi i32 [ %66, %65 ]
  %72 = getelementptr ptr, ptr %63, i32 %71
  call void @acceptAddrDep(ptr %72)
  %73 = call ptr @getLocalAddr(ptr %72)
  %74 = load ptr, ptr %73, align 8
  call void @bplus_tree_print_tree(ptr %74, i32 %64)
  %75 = add i32 %71, 1
  br label %65

76:                                               ; preds = %65
  br label %77

77:                                               ; preds = %76, %57
  br label %78

78:                                               ; preds = %77, %2
  ret void
}

define void @bplus_tree_print(ptr %0) {
  %2 = icmp eq ptr %0, null
  br i1 %2, label %3, label %5

3:                                                ; preds = %1
  %4 = call i32 (ptr, ...) @printf(ptr @str16)
  br label %23

5:                                                ; preds = %1
  %6 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  call void @acceptAddrDep(ptr %6)
  %7 = call ptr @getLocalAddr(ptr %6)
  %8 = load i32, ptr %7, align 4
  %9 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 2
  call void @acceptAddrDep(ptr %9)
  %10 = call ptr @getLocalAddr(ptr %9)
  %11 = load i32, ptr %10, align 4
  %12 = call i32 (ptr, ...) @printf(ptr @str17, i32 332, i32 %8, i32 %11)
  call void @acceptAddrDep(ptr %0)
  %13 = call ptr @getLocalAddr(ptr %0)
  %14 = load ptr, ptr %13, align 8
  %15 = icmp eq ptr %14, null
  br i1 %15, label %16, label %18

16:                                               ; preds = %5
  %17 = call i32 (ptr, ...) @printf(ptr @str4)
  br label %22

18:                                               ; preds = %5
  call void @acceptAddrDep(ptr %0)
  %19 = call ptr @getLocalAddr(ptr %0)
  %20 = load ptr, ptr %19, align 8
  call void @bplus_tree_print_tree(ptr %20, i32 0)
  %21 = call i32 (ptr, ...) @printf(ptr @str7)
  br label %22

22:                                               ; preds = %16, %18
  br label %23

23:                                               ; preds = %3, %22
  ret void
}

define void @bplus_node_shift_keys_left(ptr %0, i32 %1) {
  %3 = icmp eq ptr %0, null
  br i1 %3, label %4, label %5

4:                                                ; preds = %2
  br label %7

5:                                                ; preds = %2
  %6 = icmp slt i32 %1, 0
  br label %7

7:                                                ; preds = %4, %5
  %8 = phi i1 [ %6, %5 ], [ true, %4 ]
  br label %9

9:                                                ; preds = %7
  %10 = xor i1 %8, true
  br i1 %10, label %11, label %70

11:                                               ; preds = %9
  %12 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  %13 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 2
  br label %14

14:                                               ; preds = %20, %11
  %15 = phi i32 [ %22, %20 ], [ %1, %11 ]
  call void @acceptAddrDep(ptr %12)
  %16 = call ptr @getLocalAddr(ptr %12)
  %17 = load i32, ptr %16, align 4
  %18 = add i32 %17, -1
  %19 = icmp slt i32 %15, %18
  br i1 %19, label %20, label %28

20:                                               ; preds = %14
  %21 = phi i32 [ %15, %14 ]
  %22 = add i32 %21, 1
  %23 = getelementptr i32, ptr %13, i32 %22
  call void @acceptAddrDep(ptr %23)
  %24 = call ptr @getLocalAddr(ptr %23)
  %25 = load i32, ptr %24, align 4
  %26 = getelementptr i32, ptr %13, i32 %21
  %27 = call ptr @getLocalAddr(ptr %26)
  store i32 %25, ptr %27, align 4
  br label %14

28:                                               ; preds = %14
  call void @acceptAddrDep(ptr %0)
  %29 = call ptr @getLocalAddr(ptr %0)
  %30 = load i32, ptr %29, align 4
  %31 = icmp eq i32 %30, 1
  br i1 %31, label %32, label %53

32:                                               ; preds = %28
  %33 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 4
  br label %34

34:                                               ; preds = %40, %32
  %35 = phi i32 [ %45, %40 ], [ %1, %32 ]
  call void @acceptAddrDep(ptr %12)
  %36 = call ptr @getLocalAddr(ptr %12)
  %37 = load i32, ptr %36, align 4
  %38 = add i32 %37, -1
  %39 = icmp slt i32 %35, %38
  br i1 %39, label %40, label %52

40:                                               ; preds = %34
  %41 = phi i32 [ %35, %34 ]
  call void @acceptAddrDep(ptr %33)
  %42 = call ptr @getLocalAddr(ptr %33)
  %43 = load ptr, ptr %42, align 8
  %44 = sext i32 %41 to i64
  %45 = add i32 %41, 1
  %46 = sext i32 %45 to i64
  %47 = getelementptr i32, ptr %43, i64 %46
  call void @acceptAddrDep(ptr %47)
  %48 = call ptr @getLocalAddr(ptr %47)
  %49 = load i32, ptr %48, align 4
  %50 = getelementptr i32, ptr %43, i64 %44
  %51 = call ptr @getLocalAddr(ptr %50)
  store i32 %49, ptr %51, align 4
  br label %34

52:                                               ; preds = %34, %56
  br label %69

53:                                               ; preds = %28
  %54 = add i32 %1, 1
  %55 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 3
  br label %56

56:                                               ; preds = %61, %53
  %57 = phi i32 [ %63, %61 ], [ %54, %53 ]
  call void @acceptAddrDep(ptr %12)
  %58 = call ptr @getLocalAddr(ptr %12)
  %59 = load i32, ptr %58, align 4
  %60 = icmp slt i32 %57, %59
  br i1 %60, label %61, label %52

61:                                               ; preds = %56
  %62 = phi i32 [ %57, %56 ]
  %63 = add i32 %62, 1
  %64 = getelementptr ptr, ptr %55, i32 %63
  call void @acceptAddrDep(ptr %64)
  %65 = call ptr @getLocalAddr(ptr %64)
  %66 = load ptr, ptr %65, align 8
  %67 = getelementptr ptr, ptr %55, i32 %62
  call void @addAddrDep(ptr %67, ptr %66)
  %68 = call ptr @getLocalAddr(ptr %67)
  store ptr %66, ptr %68, align 8
  br label %56

69:                                               ; preds = %52
  br label %70

70:                                               ; preds = %69, %9
  ret void
}

define i8 @bplus_tree_delete(ptr %0, i32 %1) {
  %3 = alloca ptr, i64 1, align 8
  %4 = icmp eq ptr %0, null
  br i1 %4, label %5, label %6

5:                                                ; preds = %2
  br label %10

6:                                                ; preds = %2
  call void @acceptAddrDep(ptr %0)
  %7 = call ptr @getLocalAddr(ptr %0)
  %8 = load ptr, ptr %7, align 8
  %9 = icmp eq ptr %8, null
  br label %10

10:                                               ; preds = %5, %6
  %11 = phi i1 [ %9, %6 ], [ true, %5 ]
  br label %12

12:                                               ; preds = %10
  %13 = select i1 %11, i8 0, i8 undef
  br i1 %11, label %14, label %18

14:                                               ; preds = %42, %12
  %15 = phi i32 [ %43, %42 ], [ undef, %12 ]
  %16 = phi i1 [ %44, %42 ], [ false, %12 ]
  %17 = phi i8 [ %45, %42 ], [ %13, %12 ]
  br label %46

18:                                               ; preds = %12
  %19 = call ptr @bplus_tree_find_leaf(ptr %0, i32 %1)
  %20 = getelementptr ptr, ptr %3, i32 0
  call void @addAddrDep(ptr %20, ptr %19)
  %21 = call ptr @getLocalAddr(ptr %20)
  store ptr %19, ptr %21, align 8
  %22 = icmp eq ptr %19, null
  br i1 %22, label %23, label %24

23:                                               ; preds = %18
  br label %42

24:                                               ; preds = %18
  %25 = call i32 @bplus_node_find_key_index(ptr %19, i32 %1)
  %26 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %19, i32 0, i32 1
  call void @acceptAddrDep(ptr %26)
  %27 = call ptr @getLocalAddr(ptr %26)
  %28 = load i32, ptr %27, align 4
  %29 = icmp sge i32 %25, %28
  br i1 %29, label %30, label %31

30:                                               ; preds = %24
  br label %37

31:                                               ; preds = %24
  %32 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %19, i32 0, i32 2
  %33 = getelementptr i32, ptr %32, i32 %25
  call void @acceptAddrDep(ptr %33)
  %34 = call ptr @getLocalAddr(ptr %33)
  %35 = load i32, ptr %34, align 4
  %36 = icmp ne i32 %35, %1
  br label %37

37:                                               ; preds = %30, %31
  %38 = phi i1 [ %36, %31 ], [ true, %30 ]
  br label %39

39:                                               ; preds = %37
  %40 = xor i1 %38, true
  %41 = select i1 %38, i8 0, i8 %13
  br label %42

42:                                               ; preds = %23, %39
  %43 = phi i32 [ %25, %39 ], [ undef, %23 ]
  %44 = phi i1 [ %40, %39 ], [ false, %23 ]
  %45 = phi i8 [ %41, %39 ], [ %13, %23 ]
  br label %14

46:                                               ; preds = %14
  %47 = phi i32 [ %15, %14 ]
  %48 = phi i1 [ %16, %14 ]
  %49 = phi i8 [ %17, %14 ]
  br label %50

50:                                               ; preds = %46
  %51 = select i1 %48, i8 1, i8 %49
  br i1 %48, label %52, label %61

52:                                               ; preds = %50
  %53 = getelementptr ptr, ptr %3, i32 0
  call void @acceptAddrDep(ptr %53)
  %54 = call ptr @getLocalAddr(ptr %53)
  %55 = load ptr, ptr %54, align 8
  call void @bplus_node_shift_keys_left(ptr %55, i32 %47)
  %56 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %55, i32 0, i32 1
  call void @acceptAddrDep(ptr %56)
  %57 = call ptr @getLocalAddr(ptr %56)
  %58 = load i32, ptr %57, align 4
  %59 = add i32 %58, -1
  %60 = call ptr @getLocalAddr(ptr %56)
  store i32 %59, ptr %60, align 4
  br label %61

61:                                               ; preds = %52, %50
  ret i8 %51
}

define void @bplus_node_lock(ptr %0, i32 %1) {
  %3 = icmp eq ptr %0, null
  br i1 %3, label %4, label %5

4:                                                ; preds = %2
  br label %11

5:                                                ; preds = %2
  %6 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 8
  call void @acceptAddrDep(ptr %6)
  %7 = call ptr @getLocalAddr(ptr %6)
  %8 = load i8, ptr %7, align 1
  %9 = zext i8 %8 to i32
  %10 = icmp ne i32 %9, 0
  br label %11

11:                                               ; preds = %4, %5
  %12 = phi i1 [ %10, %5 ], [ true, %4 ]
  br label %13

13:                                               ; preds = %11
  %14 = xor i1 %12, true
  br i1 %14, label %15, label %27

15:                                               ; preds = %13
  br label %16

16:                                               ; preds = %15
  switch i32 %1, label %25 [
    i32 1, label %17
    i32 2, label %21
  ]

17:                                               ; preds = %16
  %18 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 7
  %19 = call ptr @getLocalAddr(ptr %18)
  %20 = call i32 @pthread_rwlock_rdlock(ptr %19)
  br label %25

21:                                               ; preds = %16
  %22 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 7
  %23 = call ptr @getLocalAddr(ptr %22)
  %24 = call i32 @pthread_rwlock_wrlock(ptr %23)
  br label %25

25:                                               ; preds = %17, %21, %16
  br label %26

26:                                               ; preds = %25
  br label %27

27:                                               ; preds = %26, %13
  ret void
}

declare i32 @pthread_rwlock_rdlock(ptr)

define void @bplus_node_unlock(ptr %0) {
  %2 = icmp eq ptr %0, null
  br i1 %2, label %3, label %4

3:                                                ; preds = %1
  br label %10

4:                                                ; preds = %1
  %5 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 8
  call void @acceptAddrDep(ptr %5)
  %6 = call ptr @getLocalAddr(ptr %5)
  %7 = load i8, ptr %6, align 1
  %8 = zext i8 %7 to i32
  %9 = icmp ne i32 %8, 0
  br label %10

10:                                               ; preds = %3, %4
  %11 = phi i1 [ %9, %4 ], [ true, %3 ]
  br label %12

12:                                               ; preds = %10
  %13 = xor i1 %11, true
  br i1 %13, label %14, label %18

14:                                               ; preds = %12
  %15 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 7
  %16 = call ptr @getLocalAddr(ptr %15)
  %17 = call i32 @pthread_rwlock_unlock(ptr %16)
  br label %18

18:                                               ; preds = %14, %12
  ret void
}

define i8 @bplus_node_try_lock(ptr %0, i32 %1) {
  %3 = icmp eq ptr %0, null
  br i1 %3, label %4, label %5

4:                                                ; preds = %2
  br label %11

5:                                                ; preds = %2
  %6 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 8
  call void @acceptAddrDep(ptr %6)
  %7 = call ptr @getLocalAddr(ptr %6)
  %8 = load i8, ptr %7, align 1
  %9 = zext i8 %8 to i32
  %10 = icmp ne i32 %9, 0
  br label %11

11:                                               ; preds = %4, %5
  %12 = phi i1 [ %10, %5 ], [ true, %4 ]
  br label %13

13:                                               ; preds = %11
  %14 = select i1 %12, i8 0, i8 undef
  br i1 %12, label %15, label %17

15:                                               ; preds = %37, %13
  %16 = phi i8 [ %38, %37 ], [ %14, %13 ]
  br label %39

17:                                               ; preds = %13
  br label %18

18:                                               ; preds = %17
  switch i32 %1, label %27 [
    i32 1, label %19
    i32 2, label %23
    i32 0, label %42
  ]

19:                                               ; preds = %18
  %20 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 7
  %21 = call ptr @getLocalAddr(ptr %20)
  %22 = call i32 @pthread_rwlock_tryrdlock(ptr %21)
  br label %27

23:                                               ; preds = %18
  %24 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 7
  %25 = call ptr @getLocalAddr(ptr %24)
  %26 = call i32 @pthread_rwlock_trywrlock(ptr %25)
  br label %27

27:                                               ; preds = %19, %23, %42, %18
  %28 = phi i32 [ %43, %42 ], [ %26, %23 ], [ %22, %19 ], [ undef, %18 ]
  %29 = phi i1 [ %44, %42 ], [ true, %23 ], [ true, %19 ], [ true, %18 ]
  br label %30

30:                                               ; preds = %27
  %31 = phi i32 [ %28, %27 ]
  %32 = phi i1 [ %29, %27 ]
  br i1 %32, label %33, label %36

33:                                               ; preds = %30
  %34 = icmp eq i32 %31, 0
  %35 = zext i1 %34 to i8
  br label %37

36:                                               ; preds = %30
  br label %37

37:                                               ; preds = %33, %36
  %38 = phi i8 [ %14, %36 ], [ %35, %33 ]
  br label %15

39:                                               ; preds = %15
  %40 = phi i8 [ %16, %15 ]
  br label %41

41:                                               ; preds = %39
  ret i8 %40

42:                                               ; preds = %18
  %43 = phi i32 [ undef, %18 ]
  %44 = phi i1 [ false, %18 ]
  br label %27
}

declare i32 @pthread_rwlock_tryrdlock(ptr)

declare i32 @pthread_rwlock_trywrlock(ptr)

define void @bplus_release_locks(ptr %0, i32 %1) {
  %3 = sext i32 %1 to i64
  br label %4

4:                                                ; preds = %18, %2
  %5 = phi i64 [ %19, %18 ], [ 0, %2 ]
  %6 = icmp slt i64 %5, %3
  br i1 %6, label %7, label %20

7:                                                ; preds = %4
  %8 = getelementptr ptr, ptr %0, i64 %5
  call void @acceptAddrDep(ptr %8)
  %9 = call ptr @getLocalAddr(ptr %8)
  %10 = load ptr, ptr %9, align 8
  %11 = icmp ne ptr %10, null
  br i1 %11, label %12, label %18

12:                                               ; preds = %7
  %13 = getelementptr ptr, ptr %0, i64 %5
  call void @acceptAddrDep(ptr %13)
  %14 = call ptr @getLocalAddr(ptr %13)
  %15 = load ptr, ptr %14, align 8
  call void @bplus_node_unlock(ptr %15)
  %16 = getelementptr ptr, ptr %0, i64 %5
  call void @addAddrDep(ptr %16, ptr null)
  %17 = call ptr @getLocalAddr(ptr %16)
  store ptr null, ptr %17, align 8
  br label %18

18:                                               ; preds = %12, %7
  %19 = add i64 %5, 1
  br label %4

20:                                               ; preds = %4
  ret void
}

define i8 @bplus_node_is_safe_for_insert(ptr %0) {
  %2 = icmp eq ptr %0, null
  br i1 %2, label %3, label %4

3:                                                ; preds = %1
  br label %8

4:                                                ; preds = %1
  %5 = call i8 @bplus_node_is_full(ptr %0)
  %6 = icmp eq i8 %5, 0
  %7 = zext i1 %6 to i8
  br label %8

8:                                                ; preds = %3, %4
  %9 = phi i8 [ %7, %4 ], [ 0, %3 ]
  br label %10

10:                                               ; preds = %8
  ret i8 %9
}

define i8 @bplus_node_is_safe_for_delete(ptr %0) {
  %2 = icmp eq ptr %0, null
  %3 = xor i1 %2, true
  %4 = select i1 %2, i8 0, i8 undef
  %5 = select i1 %3, i8 1, i8 %4
  ret i8 %5
}

define ptr @bplus_tree_find_leaf_concurrent(ptr %0, i32 %1, ptr %2, ptr %3) {
  %5 = icmp eq ptr %0, null
  br i1 %5, label %6, label %7

6:                                                ; preds = %4
  br label %11

7:                                                ; preds = %4
  call void @acceptAddrDep(ptr %0)
  %8 = call ptr @getLocalAddr(ptr %0)
  %9 = load ptr, ptr %8, align 8
  %10 = icmp eq ptr %9, null
  br label %11

11:                                               ; preds = %6, %7
  %12 = phi i1 [ %10, %7 ], [ true, %6 ]
  br label %13

13:                                               ; preds = %11
  br i1 %12, label %14, label %16

14:                                               ; preds = %115, %13
  %15 = phi ptr [ %116, %115 ], [ null, %13 ]
  br label %117

16:                                               ; preds = %13
  %17 = call ptr @getLocalAddr(ptr %3)
  store i32 0, ptr %17, align 4
  %18 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 4
  %19 = call ptr @getLocalAddr(ptr %18)
  %20 = call i32 @pthread_mutex_lock(ptr %19)
  call void @acceptAddrDep(ptr %0)
  %21 = call ptr @getLocalAddr(ptr %0)
  %22 = load ptr, ptr %21, align 8
  %23 = icmp eq ptr %22, null
  br i1 %23, label %24, label %27

24:                                               ; preds = %16
  %25 = call ptr @getLocalAddr(ptr %18)
  %26 = call i32 @pthread_mutex_unlock(ptr %25)
  br label %115

27:                                               ; preds = %16
  call void @bplus_node_lock(ptr %22, i32 1)
  call void @acceptAddrDep(ptr %3)
  %28 = call ptr @getLocalAddr(ptr %3)
  %29 = load i32, ptr %28, align 4
  %30 = add i32 %29, 1
  %31 = call ptr @getLocalAddr(ptr %3)
  store i32 %30, ptr %31, align 4
  %32 = sext i32 %29 to i64
  %33 = getelementptr ptr, ptr %2, i64 %32
  call void @addAddrDep(ptr %33, ptr %22)
  %34 = call ptr @getLocalAddr(ptr %33)
  store ptr %22, ptr %34, align 8
  %35 = call ptr @getLocalAddr(ptr %18)
  %36 = call i32 @pthread_mutex_unlock(ptr %35)
  br label %37

37:                                               ; preds = %113, %27
  %38 = phi i1 [ %78, %113 ], [ true, %27 ]
  %39 = phi ptr [ %79, %113 ], [ %22, %27 ]
  %40 = icmp ne ptr %39, null
  br i1 %40, label %41, label %45

41:                                               ; preds = %37
  call void @acceptAddrDep(ptr %39)
  %42 = call ptr @getLocalAddr(ptr %39)
  %43 = load i32, ptr %42, align 4
  %44 = icmp eq i32 %43, 0
  br label %46

45:                                               ; preds = %37
  br label %46

46:                                               ; preds = %41, %45
  %47 = phi i1 [ false, %45 ], [ %44, %41 ]
  br label %48

48:                                               ; preds = %46
  %49 = and i1 %47, %38
  br i1 %49, label %50, label %114

50:                                               ; preds = %48
  %51 = phi ptr [ %39, %48 ]
  %52 = call i32 @bplus_node_find_key_index(ptr %51, i32 %1)
  %53 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %51, i32 0, i32 1
  call void @acceptAddrDep(ptr %53)
  %54 = call ptr @getLocalAddr(ptr %53)
  %55 = load i32, ptr %54, align 4
  %56 = icmp slt i32 %52, %55
  br i1 %56, label %57, label %68

57:                                               ; preds = %50
  %58 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %51, i32 0, i32 2
  %59 = getelementptr i32, ptr %58, i32 %52
  call void @acceptAddrDep(ptr %59)
  %60 = call ptr @getLocalAddr(ptr %59)
  %61 = load i32, ptr %60, align 4
  %62 = icmp sge i32 %1, %61
  br i1 %62, label %63, label %65

63:                                               ; preds = %57
  %64 = add i32 %52, 1
  br label %66

65:                                               ; preds = %57
  br label %66

66:                                               ; preds = %63, %65
  %67 = phi i32 [ %52, %65 ], [ %64, %63 ]
  br label %68

68:                                               ; preds = %66, %50
  %69 = phi i32 [ %67, %66 ], [ %52, %50 ]
  br label %70

70:                                               ; preds = %68
  %71 = phi i32 [ %69, %68 ]
  br label %72

72:                                               ; preds = %70
  %73 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %51, i32 0, i32 3
  %74 = getelementptr ptr, ptr %73, i32 %71
  call void @acceptAddrDep(ptr %74)
  %75 = call ptr @getLocalAddr(ptr %74)
  %76 = load ptr, ptr %75, align 8
  %77 = icmp eq ptr %76, null
  %78 = xor i1 %77, true
  %79 = select i1 %77, ptr %51, ptr %76
  br i1 %77, label %80, label %81

80:                                               ; preds = %72
  br label %112

81:                                               ; preds = %72
  call void @bplus_node_lock(ptr %76, i32 1)
  call void @acceptAddrDep(ptr %3)
  %82 = call ptr @getLocalAddr(ptr %3)
  %83 = load i32, ptr %82, align 4
  %84 = add i32 %83, 1
  %85 = call ptr @getLocalAddr(ptr %3)
  store i32 %84, ptr %85, align 4
  %86 = sext i32 %83 to i64
  %87 = getelementptr ptr, ptr %2, i64 %86
  call void @addAddrDep(ptr %87, ptr %76)
  %88 = call ptr @getLocalAddr(ptr %87)
  store ptr %76, ptr %88, align 8
  call void @acceptAddrDep(ptr %2)
  %89 = call ptr @getLocalAddr(ptr %2)
  %90 = load ptr, ptr %89, align 8
  call void @bplus_node_unlock(ptr %90)
  br label %91

91:                                               ; preds = %97, %81
  %92 = phi i32 [ %100, %97 ], [ 0, %81 ]
  call void @acceptAddrDep(ptr %3)
  %93 = call ptr @getLocalAddr(ptr %3)
  %94 = load i32, ptr %93, align 4
  %95 = add i32 %94, -1
  %96 = icmp slt i32 %92, %95
  br i1 %96, label %97, label %107

97:                                               ; preds = %91
  %98 = phi i32 [ %92, %91 ]
  %99 = sext i32 %98 to i64
  %100 = add i32 %98, 1
  %101 = sext i32 %100 to i64
  %102 = getelementptr ptr, ptr %2, i64 %101
  call void @acceptAddrDep(ptr %102)
  %103 = call ptr @getLocalAddr(ptr %102)
  %104 = load ptr, ptr %103, align 8
  %105 = getelementptr ptr, ptr %2, i64 %99
  call void @addAddrDep(ptr %105, ptr %104)
  %106 = call ptr @getLocalAddr(ptr %105)
  store ptr %104, ptr %106, align 8
  br label %91

107:                                              ; preds = %91
  call void @acceptAddrDep(ptr %3)
  %108 = call ptr @getLocalAddr(ptr %3)
  %109 = load i32, ptr %108, align 4
  %110 = add i32 %109, -1
  %111 = call ptr @getLocalAddr(ptr %3)
  store i32 %110, ptr %111, align 4
  br label %112

112:                                              ; preds = %80, %107
  br label %113

113:                                              ; preds = %112
  br label %37

114:                                              ; preds = %48
  br label %115

115:                                              ; preds = %24, %114
  %116 = phi ptr [ %39, %114 ], [ null, %24 ]
  br label %14

117:                                              ; preds = %14
  %118 = phi ptr [ %15, %14 ]
  br label %119

119:                                              ; preds = %117
  ret ptr %118
}

declare i32 @pthread_mutex_lock(ptr)

declare i32 @pthread_mutex_unlock(ptr)

define i8 @bplus_tree_concurrent_search(ptr %0, i32 %1, ptr %2) {
  %4 = alloca i32, i64 1, align 4
  %5 = call ptr @getLocalAddr(ptr %4)
  store i32 undef, ptr %5, align 4
  %6 = alloca ptr, i64 32, align 8
  %7 = icmp eq ptr %0, null
  br i1 %7, label %8, label %9

8:                                                ; preds = %3
  br label %53

9:                                                ; preds = %3
  %10 = call ptr @getLocalAddr(ptr %4)
  store i32 0, ptr %10, align 4
  %11 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 3
  %12 = call ptr @getLocalAddr(ptr %11)
  %13 = call i32 @pthread_rwlock_rdlock(ptr %12)
  %14 = call ptr @bplus_tree_find_leaf_concurrent(ptr %0, i32 %1, ptr %6, ptr %4)
  %15 = icmp ne ptr %14, null
  br i1 %15, label %16, label %44

16:                                               ; preds = %9
  %17 = call i32 @bplus_node_find_key_index(ptr %14, i32 %1)
  %18 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %14, i32 0, i32 1
  call void @acceptAddrDep(ptr %18)
  %19 = call ptr @getLocalAddr(ptr %18)
  %20 = load i32, ptr %19, align 4
  %21 = icmp slt i32 %17, %20
  br i1 %21, label %22, label %40

22:                                               ; preds = %16
  %23 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %14, i32 0, i32 2
  %24 = getelementptr i32, ptr %23, i32 %17
  call void @acceptAddrDep(ptr %24)
  %25 = call ptr @getLocalAddr(ptr %24)
  %26 = load i32, ptr %25, align 4
  %27 = icmp eq i32 %26, %1
  %28 = icmp ne ptr %2, null
  %29 = and i1 %27, %28
  %30 = zext i1 %27 to i8
  br i1 %29, label %31, label %40

31:                                               ; preds = %22
  %32 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %14, i32 0, i32 4
  call void @acceptAddrDep(ptr %32)
  %33 = call ptr @getLocalAddr(ptr %32)
  %34 = load ptr, ptr %33, align 8
  %35 = sext i32 %17 to i64
  %36 = getelementptr i32, ptr %34, i64 %35
  call void @acceptAddrDep(ptr %36)
  %37 = call ptr @getLocalAddr(ptr %36)
  %38 = load i32, ptr %37, align 4
  %39 = call ptr @getLocalAddr(ptr %2)
  store i32 %38, ptr %39, align 4
  br label %40

40:                                               ; preds = %31, %22, %16
  %41 = phi i8 [ %30, %31 ], [ %30, %22 ], [ 0, %16 ]
  br label %42

42:                                               ; preds = %40
  %43 = phi i8 [ %41, %40 ]
  br label %44

44:                                               ; preds = %42, %9
  %45 = phi i8 [ %43, %42 ], [ 0, %9 ]
  br label %46

46:                                               ; preds = %44
  %47 = phi i8 [ %45, %44 ]
  br label %48

48:                                               ; preds = %46
  call void @acceptAddrDep(ptr %4)
  %49 = call ptr @getLocalAddr(ptr %4)
  %50 = load i32, ptr %49, align 4
  call void @bplus_release_locks(ptr %6, i32 %50)
  %51 = call ptr @getLocalAddr(ptr %11)
  %52 = call i32 @pthread_rwlock_unlock(ptr %51)
  br label %53

53:                                               ; preds = %8, %48
  %54 = phi i8 [ %47, %48 ], [ 0, %8 ]
  br label %55

55:                                               ; preds = %53
  ret i8 %54
}

define i8 @bplus_tree_concurrent_insert(ptr %0, i32 %1, i32 %2) {
  %4 = alloca ptr, i64 32, align 8
  %5 = icmp eq ptr %0, null
  %6 = select i1 %5, i8 0, i8 undef
  br i1 %5, label %7, label %9

7:                                                ; preds = %196, %3
  %8 = phi i8 [ %197, %196 ], [ %6, %3 ]
  br label %198

9:                                                ; preds = %3
  %10 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 3
  %11 = call ptr @getLocalAddr(ptr %10)
  %12 = call i32 @pthread_rwlock_rdlock(ptr %11)
  %13 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 4
  %14 = call ptr @getLocalAddr(ptr %13)
  %15 = call i32 @pthread_mutex_lock(ptr %14)
  call void @acceptAddrDep(ptr %0)
  %16 = call ptr @getLocalAddr(ptr %0)
  %17 = load ptr, ptr %16, align 8
  %18 = icmp eq ptr %17, null
  br i1 %18, label %19, label %74

19:                                               ; preds = %9
  %20 = call ptr @getLocalAddr(ptr %13)
  %21 = call i32 @pthread_mutex_unlock(ptr %20)
  %22 = call ptr @getLocalAddr(ptr %10)
  %23 = call i32 @pthread_rwlock_unlock(ptr %22)
  %24 = call ptr @getLocalAddr(ptr %10)
  %25 = call i32 @pthread_rwlock_wrlock(ptr %24)
  %26 = call ptr @getLocalAddr(ptr %13)
  %27 = call i32 @pthread_mutex_lock(ptr %26)
  call void @acceptAddrDep(ptr %0)
  %28 = call ptr @getLocalAddr(ptr %0)
  %29 = load ptr, ptr %28, align 8
  %30 = icmp eq ptr %29, null
  br i1 %30, label %31, label %66

31:                                               ; preds = %19
  %32 = call ptr @bplus_node_create(i32 1)
  call void @addAddrDep(ptr %0, ptr %32)
  %33 = call ptr @getLocalAddr(ptr %0)
  store ptr %32, ptr %33, align 8
  call void @acceptAddrDep(ptr %0)
  %34 = call ptr @getLocalAddr(ptr %0)
  %35 = load ptr, ptr %34, align 8
  %36 = icmp eq ptr %35, null
  br i1 %36, label %37, label %42

37:                                               ; preds = %31
  %38 = call ptr @getLocalAddr(ptr %13)
  %39 = call i32 @pthread_mutex_unlock(ptr %38)
  %40 = call ptr @getLocalAddr(ptr %10)
  %41 = call i32 @pthread_rwlock_unlock(ptr %40)
  br label %65

42:                                               ; preds = %31
  call void @acceptAddrDep(ptr %0)
  %43 = call ptr @getLocalAddr(ptr %0)
  %44 = load ptr, ptr %43, align 8
  %45 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %44, i32 0, i32 2
  %46 = call ptr @getLocalAddr(ptr %45)
  store i32 %1, ptr %46, align 4
  call void @acceptAddrDep(ptr %0)
  %47 = call ptr @getLocalAddr(ptr %0)
  %48 = load ptr, ptr %47, align 8
  %49 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %48, i32 0, i32 4
  call void @acceptAddrDep(ptr %49)
  %50 = call ptr @getLocalAddr(ptr %49)
  %51 = load ptr, ptr %50, align 8
  %52 = call ptr @getLocalAddr(ptr %51)
  store i32 %2, ptr %52, align 4
  call void @acceptAddrDep(ptr %0)
  %53 = call ptr @getLocalAddr(ptr %0)
  %54 = load ptr, ptr %53, align 8
  %55 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %54, i32 0, i32 1
  %56 = call ptr @getLocalAddr(ptr %55)
  store i32 1, ptr %56, align 4
  %57 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 1
  %58 = call ptr @getLocalAddr(ptr %57)
  store i32 1, ptr %58, align 4
  %59 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 2
  %60 = call ptr @getLocalAddr(ptr %59)
  store i32 1, ptr %60, align 4
  %61 = call ptr @getLocalAddr(ptr %13)
  %62 = call i32 @pthread_mutex_unlock(ptr %61)
  %63 = call ptr @getLocalAddr(ptr %10)
  %64 = call i32 @pthread_rwlock_unlock(ptr %63)
  br label %65

65:                                               ; preds = %37, %42
  br label %72

66:                                               ; preds = %19
  %67 = call ptr @getLocalAddr(ptr %13)
  %68 = call i32 @pthread_mutex_unlock(ptr %67)
  %69 = call ptr @getLocalAddr(ptr %10)
  %70 = call i32 @pthread_rwlock_unlock(ptr %69)
  %71 = call i8 @bplus_tree_concurrent_insert(ptr %0, i32 %1, i32 %2)
  br label %72

72:                                               ; preds = %65, %66, %194
  %73 = phi i8 [ %195, %194 ], [ %6, %66 ], [ %6, %65 ]
  br label %196

74:                                               ; preds = %9
  call void @acceptAddrDep(ptr %0)
  %75 = call ptr @getLocalAddr(ptr %0)
  %76 = load ptr, ptr %75, align 8
  call void @bplus_node_lock(ptr %76, i32 2)
  call void @addAddrDep(ptr %4, ptr %76)
  %77 = call ptr @getLocalAddr(ptr %4)
  store ptr %76, ptr %77, align 8
  %78 = call ptr @getLocalAddr(ptr %13)
  %79 = call i32 @pthread_mutex_unlock(ptr %78)
  br label %80

80:                                               ; preds = %142, %74
  %81 = phi i32 [ %141, %142 ], [ 1, %74 ]
  %82 = phi i1 [ %117, %142 ], [ true, %74 ]
  %83 = phi ptr [ %118, %142 ], [ %76, %74 ]
  call void @acceptAddrDep(ptr %83)
  %84 = call ptr @getLocalAddr(ptr %83)
  %85 = load i32, ptr %84, align 4
  %86 = icmp eq i32 %85, 0
  %87 = and i1 %86, %82
  br i1 %87, label %88, label %143

88:                                               ; preds = %80
  %89 = phi i32 [ %81, %80 ]
  %90 = phi ptr [ %83, %80 ]
  %91 = call i32 @bplus_node_find_key_index(ptr %90, i32 %1)
  %92 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %90, i32 0, i32 1
  call void @acceptAddrDep(ptr %92)
  %93 = call ptr @getLocalAddr(ptr %92)
  %94 = load i32, ptr %93, align 4
  %95 = icmp slt i32 %91, %94
  br i1 %95, label %96, label %107

96:                                               ; preds = %88
  %97 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %90, i32 0, i32 2
  %98 = getelementptr i32, ptr %97, i32 %91
  call void @acceptAddrDep(ptr %98)
  %99 = call ptr @getLocalAddr(ptr %98)
  %100 = load i32, ptr %99, align 4
  %101 = icmp sge i32 %1, %100
  br i1 %101, label %102, label %104

102:                                              ; preds = %96
  %103 = add i32 %91, 1
  br label %105

104:                                              ; preds = %96
  br label %105

105:                                              ; preds = %102, %104
  %106 = phi i32 [ %91, %104 ], [ %103, %102 ]
  br label %107

107:                                              ; preds = %105, %88
  %108 = phi i32 [ %106, %105 ], [ %91, %88 ]
  br label %109

109:                                              ; preds = %107
  %110 = phi i32 [ %108, %107 ]
  br label %111

111:                                              ; preds = %109
  %112 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %90, i32 0, i32 3
  %113 = getelementptr ptr, ptr %112, i32 %110
  call void @acceptAddrDep(ptr %113)
  %114 = call ptr @getLocalAddr(ptr %113)
  %115 = load ptr, ptr %114, align 8
  %116 = icmp eq ptr %115, null
  %117 = xor i1 %116, true
  %118 = select i1 %116, ptr %90, ptr %115
  br i1 %116, label %119, label %121

119:                                              ; preds = %138, %121, %111
  %120 = phi i32 [ %128, %138 ], [ %128, %121 ], [ %89, %111 ]
  br label %140

121:                                              ; preds = %111
  call void @bplus_node_lock(ptr %115, i32 2)
  %122 = add i32 %89, 1
  %123 = sext i32 %89 to i64
  %124 = getelementptr ptr, ptr %4, i64 %123
  call void @addAddrDep(ptr %124, ptr %115)
  %125 = call ptr @getLocalAddr(ptr %124)
  store ptr %115, ptr %125, align 8
  %126 = call i8 @bplus_node_is_safe_for_insert(ptr %115)
  %127 = icmp ne i8 %126, 0
  %128 = select i1 %127, i32 1, i32 %122
  br i1 %127, label %129, label %119

129:                                              ; preds = %121
  br label %130

130:                                              ; preds = %133, %129
  %131 = phi i64 [ %137, %133 ], [ 0, %129 ]
  %132 = icmp slt i64 %131, %123
  br i1 %132, label %133, label %138

133:                                              ; preds = %130
  %134 = getelementptr ptr, ptr %4, i64 %131
  call void @acceptAddrDep(ptr %134)
  %135 = call ptr @getLocalAddr(ptr %134)
  %136 = load ptr, ptr %135, align 8
  call void @bplus_node_unlock(ptr %136)
  %137 = add i64 %131, 1
  br label %130

138:                                              ; preds = %130
  call void @addAddrDep(ptr %4, ptr %115)
  %139 = call ptr @getLocalAddr(ptr %4)
  store ptr %115, ptr %139, align 8
  br label %119

140:                                              ; preds = %119
  %141 = phi i32 [ %120, %119 ]
  br label %142

142:                                              ; preds = %140
  br label %80

143:                                              ; preds = %80
  %144 = call i32 @bplus_node_find_key_index(ptr %83, i32 %1)
  %145 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %83, i32 0, i32 1
  call void @acceptAddrDep(ptr %145)
  %146 = call ptr @getLocalAddr(ptr %145)
  %147 = load i32, ptr %146, align 4
  %148 = icmp slt i32 %144, %147
  br i1 %148, label %149, label %166

149:                                              ; preds = %143
  %150 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %83, i32 0, i32 2
  %151 = getelementptr i32, ptr %150, i32 %144
  call void @acceptAddrDep(ptr %151)
  %152 = call ptr @getLocalAddr(ptr %151)
  %153 = load i32, ptr %152, align 4
  %154 = icmp eq i32 %153, %1
  %155 = icmp ne i32 %153, %1
  %156 = select i1 %154, i8 1, i8 %6
  br i1 %154, label %157, label %166

157:                                              ; preds = %149
  %158 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %83, i32 0, i32 4
  call void @acceptAddrDep(ptr %158)
  %159 = call ptr @getLocalAddr(ptr %158)
  %160 = load ptr, ptr %159, align 8
  %161 = sext i32 %144 to i64
  %162 = getelementptr i32, ptr %160, i64 %161
  %163 = call ptr @getLocalAddr(ptr %162)
  store i32 %2, ptr %163, align 4
  call void @bplus_release_locks(ptr %4, i32 %81)
  %164 = call ptr @getLocalAddr(ptr %10)
  %165 = call i32 @pthread_rwlock_unlock(ptr %164)
  br label %166

166:                                              ; preds = %157, %149, %143
  %167 = phi i1 [ %155, %157 ], [ %155, %149 ], [ true, %143 ]
  %168 = phi i8 [ %156, %157 ], [ %156, %149 ], [ %6, %143 ]
  br label %169

169:                                              ; preds = %166
  %170 = phi i1 [ %167, %166 ]
  %171 = phi i8 [ %168, %166 ]
  br label %172

172:                                              ; preds = %169
  br i1 %170, label %173, label %192

173:                                              ; preds = %172
  %174 = call i8 @bplus_node_is_full(ptr %83)
  %175 = icmp eq i8 %174, 0
  %176 = icmp ne i8 %174, 0
  br i1 %175, label %177, label %180

177:                                              ; preds = %173
  call void @bplus_node_insert_key_value(ptr %83, i32 %1, i32 %2)
  call void @bplus_release_locks(ptr %4, i32 %81)
  %178 = call ptr @getLocalAddr(ptr %10)
  %179 = call i32 @pthread_rwlock_unlock(ptr %178)
  br label %180

180:                                              ; preds = %177, %173
  br i1 %176, label %181, label %189

181:                                              ; preds = %180
  call void @bplus_release_locks(ptr %4, i32 %81)
  %182 = call ptr @getLocalAddr(ptr %10)
  %183 = call i32 @pthread_rwlock_unlock(ptr %182)
  %184 = call ptr @getLocalAddr(ptr %10)
  %185 = call i32 @pthread_rwlock_wrlock(ptr %184)
  %186 = call i8 @bplus_tree_insert(ptr %0, i32 %1, i32 %2)
  %187 = call ptr @getLocalAddr(ptr %10)
  %188 = call i32 @pthread_rwlock_unlock(ptr %187)
  br label %190

189:                                              ; preds = %180
  br label %190

190:                                              ; preds = %181, %189
  %191 = phi i8 [ %171, %189 ], [ %186, %181 ]
  br label %192

192:                                              ; preds = %190, %172
  %193 = phi i8 [ %191, %190 ], [ %6, %172 ]
  br label %194

194:                                              ; preds = %192
  %195 = phi i8 [ %193, %192 ]
  br label %72

196:                                              ; preds = %72
  %197 = phi i8 [ %73, %72 ]
  br label %7

198:                                              ; preds = %7
  %199 = phi i8 [ %8, %7 ]
  br label %200

200:                                              ; preds = %198
  ret i8 %199
}

define i8 @bplus_tree_concurrent_delete(ptr %0, i32 %1) {
  %3 = alloca ptr, i64 1, align 8
  %4 = alloca i32, i64 1, align 4
  %5 = call ptr @getLocalAddr(ptr %4)
  store i32 undef, ptr %5, align 4
  %6 = alloca ptr, i64 32, align 8
  %7 = icmp eq ptr %0, null
  %8 = select i1 %7, i8 0, i8 undef
  br i1 %7, label %9, label %13

9:                                                ; preds = %47, %2
  %10 = phi i32 [ %48, %47 ], [ undef, %2 ]
  %11 = phi i1 [ %49, %47 ], [ false, %2 ]
  %12 = phi i8 [ %50, %47 ], [ %8, %2 ]
  br label %51

13:                                               ; preds = %2
  %14 = call ptr @getLocalAddr(ptr %4)
  store i32 0, ptr %14, align 4
  %15 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 3
  %16 = call ptr @getLocalAddr(ptr %15)
  %17 = call i32 @pthread_rwlock_rdlock(ptr %16)
  %18 = call ptr @bplus_tree_find_leaf_concurrent(ptr %0, i32 %1, ptr %6, ptr %4)
  %19 = getelementptr ptr, ptr %3, i32 0
  call void @addAddrDep(ptr %19, ptr %18)
  %20 = call ptr @getLocalAddr(ptr %19)
  store ptr %18, ptr %20, align 8
  %21 = icmp eq ptr %18, null
  br i1 %21, label %22, label %25

22:                                               ; preds = %13
  %23 = call ptr @getLocalAddr(ptr %15)
  %24 = call i32 @pthread_rwlock_unlock(ptr %23)
  br label %47

25:                                               ; preds = %13
  call void @bplus_node_unlock(ptr %18)
  call void @bplus_node_lock(ptr %18, i32 2)
  %26 = call i32 @bplus_node_find_key_index(ptr %18, i32 %1)
  %27 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %18, i32 0, i32 1
  call void @acceptAddrDep(ptr %27)
  %28 = call ptr @getLocalAddr(ptr %27)
  %29 = load i32, ptr %28, align 4
  %30 = icmp sge i32 %26, %29
  br i1 %30, label %31, label %32

31:                                               ; preds = %25
  br label %38

32:                                               ; preds = %25
  %33 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %18, i32 0, i32 2
  %34 = getelementptr i32, ptr %33, i32 %26
  call void @acceptAddrDep(ptr %34)
  %35 = call ptr @getLocalAddr(ptr %34)
  %36 = load i32, ptr %35, align 4
  %37 = icmp ne i32 %36, %1
  br label %38

38:                                               ; preds = %31, %32
  %39 = phi i1 [ %37, %32 ], [ true, %31 ]
  br label %40

40:                                               ; preds = %38
  %41 = xor i1 %39, true
  %42 = select i1 %39, i8 0, i8 %8
  br i1 %39, label %43, label %46

43:                                               ; preds = %40
  call void @bplus_node_unlock(ptr %18)
  %44 = call ptr @getLocalAddr(ptr %15)
  %45 = call i32 @pthread_rwlock_unlock(ptr %44)
  br label %46

46:                                               ; preds = %43, %40
  br label %47

47:                                               ; preds = %22, %46
  %48 = phi i32 [ %26, %46 ], [ undef, %22 ]
  %49 = phi i1 [ %41, %46 ], [ false, %22 ]
  %50 = phi i8 [ %42, %46 ], [ %8, %22 ]
  br label %9

51:                                               ; preds = %9
  %52 = phi i32 [ %10, %9 ]
  %53 = phi i1 [ %11, %9 ]
  %54 = phi i8 [ %12, %9 ]
  br label %55

55:                                               ; preds = %51
  %56 = select i1 %53, i8 1, i8 %54
  br i1 %53, label %57, label %69

57:                                               ; preds = %55
  %58 = getelementptr ptr, ptr %3, i32 0
  call void @acceptAddrDep(ptr %58)
  %59 = call ptr @getLocalAddr(ptr %58)
  %60 = load ptr, ptr %59, align 8
  call void @bplus_node_shift_keys_left(ptr %60, i32 %52)
  %61 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %60, i32 0, i32 1
  call void @acceptAddrDep(ptr %61)
  %62 = call ptr @getLocalAddr(ptr %61)
  %63 = load i32, ptr %62, align 4
  %64 = add i32 %63, -1
  %65 = call ptr @getLocalAddr(ptr %61)
  store i32 %64, ptr %65, align 4
  call void @bplus_node_unlock(ptr %60)
  %66 = getelementptr %"_Converted_opaque@polygeist@<EMAIL>", ptr %0, i32 0, i32 3
  %67 = call ptr @getLocalAddr(ptr %66)
  %68 = call i32 @pthread_rwlock_unlock(ptr %67)
  br label %69

69:                                               ; preds = %57, %55
  ret i8 %56
}

define ptr @pth_bm_target_create() {
  %1 = call ptr @bplus_tree_create()
  ret ptr %1
}

define void @pth_bm_target_destroy(ptr %0) {
  call void @bplus_tree_destroy(ptr %0)
  ret void
}

define void @pth_bm_target_read(ptr %0, i32 %1) {
  %3 = call i8 @bplus_tree_concurrent_search(ptr %0, i32 %1, ptr null)
  ret void
}

define void @pth_bm_target_insert(ptr %0, i32 %1) {
  %3 = call i8 @bplus_tree_concurrent_insert(ptr %0, i32 %1, i32 48879)
  ret void
}

define void @pth_bm_target_update(ptr %0, i32 %1) {
  %3 = call i8 @bplus_tree_concurrent_insert(ptr %0, i32 %1, i32 48879)
  ret void
}

define void @pth_bm_target_delete(ptr %0, i32 %1) {
  %3 = call i8 @bplus_tree_concurrent_delete(ptr %0, i32 %1)
  ret void
}

!llvm.module.flags = !{!0}

!0 = !{i32 2, !"Debug Info Version", i32 3}
