/*
 * A simple singly-linked list
 */

#pragma once

#include <cstddef>
#include <stdexcept>
#include <utility>

template <typename V>
struct MyListNode {
    V value;
    MyListNode<V>* next;
    MyListNode(V value) : value(value), next(nullptr) {}
};

template <typename V>
struct MyList {
    MyListNode<V>* head;
    MyListNode<V>* tail;
    size_t size;

    MyList() : head(nullptr), tail(nullptr), size(0) {};
    bool is_empty() { return size == 0; }
    void push(V value) {
        auto node = new MyListNode<V>(value);
        if (is_empty()) {
            head = tail = node;
        } else {
            tail->next = node;
            tail = node;
        }
        size++;
    }

    V& operator[](size_t index) {
        auto node = head;
        for (size_t i = 0; i < index; i++) {
            if (!node) throw std::out_of_range("out of range");
            node = node->next;
        }
        if (!node) throw std::out_of_range("out of range");
        return node->value;
    }
};

template <typename K, typename V>
bool mylist_kv_contains(MyList<std::pair<K, V>> list, K& key) {
    for (auto& x = list.head; x; x = x->next) {
        if (x->value.first == key) {
            return true;
        }
    }
    return false;
}
