#define _GNU_SOURCE

#include <time.h>
#include <stdint.h>
#include <sched.h>
#include <pthread.h>
#include <stdio.h>
#include <errno.h>
#include <stdlib.h>
#include <unistd.h>
#include <stdatomic.h>
#include <stdbool.h>

#include "target.h"
#include "generators/xoshiro256p.h"
#include "generators/zipf.h"
#include "workload.h"

static int get_monotime_ns(uint64_t *out)
{
	int rc;
	struct timespec tp;

	rc = clock_gettime(CLOCK_MONOTONIC_RAW, &tp);
	if (rc != 0)
		return errno;

	*out = tp.tv_sec * 1000000000ULL + tp.tv_nsec;

	return 0;
}

static int cpu_num()
{
	cpu_set_t cs;
	CPU_ZERO(&cs);
	sched_getaffinity(0, sizeof(cpu_set_t), &cs);
	return CPU_COUNT(&cs);
}

static void *workload_thread(void *data)
{
	struct pth_workload_context *ctx = data;
	const struct pth_workload_config *cfg = ctx->config;

	uint64_t start;
	uint64_t end;
	uint64_t dur = 0;

	while (atomic_fetch_add_explicit(&ctx->report->warmup_op_count, 1,
					 memory_order_relaxed) <
	       cfg->warmup_op_num) {
		uint64_t key = pth_zipf_next(&ctx->zipf);
		double op = pth_xoshiro256p_next_f64(&ctx->uniform);

		if (op < cfg->read_ratio) {
			pth_bm_target_read(ctx->target, key);
		} else if (op < cfg->read_ratio + cfg->insert_ratio) {
			pth_bm_target_insert(ctx->target, key);
		} else if (op < cfg->read_ratio + cfg->insert_ratio +
					cfg->update_ratio) {
			pth_bm_target_update(ctx->target, key);
		} else {
			pth_bm_target_delete(ctx->target, key);
		}
	}

	while (atomic_fetch_add_explicit(&ctx->report->op_count, 1,
					 memory_order_relaxed) < cfg->op_num) {
		uint64_t key = pth_zipf_next(&ctx->zipf);
		double op = pth_xoshiro256p_next_f64(&ctx->uniform);

		// TODO: Time error handling.
		get_monotime_ns(&start);

		if (op < cfg->read_ratio) {
			pth_bm_target_read(ctx->target, key);
		} else if (op < cfg->read_ratio + cfg->insert_ratio) {
			pth_bm_target_insert(ctx->target, key);
		} else if (op < cfg->read_ratio + cfg->insert_ratio +
					cfg->update_ratio) {
			pth_bm_target_update(ctx->target, key);
		} else {
			pth_bm_target_delete(ctx->target, key);
		}

		get_monotime_ns(&end);
		dur += end - start;
	}

	struct pth_workload_report *report = ctx->report;
	pthread_mutex_lock(&report->mutex);
	report->duration += dur;
	pthread_mutex_unlock(&report->mutex);
}

static int setup_ctxs(const struct pth_workload_config *cfg, void *target,
		      struct pth_workload_report *report,
		      struct pth_workload_context **out_ctxs)
{
	int rc;

	struct pth_workload_context *ctxs =
		malloc(cfg->thread_num * sizeof(struct pth_workload_context));
	if (!ctxs) {
		printf("Cannot allocate %d contexts\n", cfg->thread_num);
		rc = -ENOMEM;
		goto err;
	}

	struct pth_xoshiro256p master_uniform;
	uint64_t seed = time(NULL);
	pth_xoshiro256p_init(&master_uniform, seed);
	printf("xoshiro256p master seed: %llu\n", seed);

	for (int i = 0; i < cfg->thread_num; ++i) {
		rc = pth_workload_context_init(&ctxs[i], cfg, target,
					       &master_uniform, report);
		if (rc != 0) {
			printf("Cannot initialize workload context for thread %d\n",
			       i);
			goto err_free_ctxs;
		}
	}

	*out_ctxs = ctxs;

	return 0;

err_free_ctxs:
	free(ctxs);
err:
	return rc;
}

int main()
{
	int rc = 0;

	int ncpu = 1;
	struct pth_workload_config cfg = {
		.thread_num = ncpu,
		.key_num = 1048576,
		.op_num = 1000000,
		.warmup_op_num = 1000000,
		.zipf_theta = 0.99,
		.read_ratio = 0.8,
		.insert_ratio = 0.05,
		.update_ratio = 0.1,
		.delete_ratio = 0.05,
	};

	printf("CPUs: %d\n", ncpu);
	printf("Threads: %d\n", cfg.thread_num);

	bool cancelled = false;
	struct pth_workload_report report;

	rc = pth_workload_report_init(&report);
	if (rc != 0) {
		printf("Cannot initialize workload report\n");
		goto exit;
	}

	pthread_t *thds = malloc(cfg.thread_num * sizeof(pthread_t));
	if (!thds) {
		printf("Cannot allocate workload thread list\n");
		rc = -ENOMEM;
		goto exit;
	}

	void *target = pth_bm_target_create();
	if (!target) {
		printf("Benchmark implementation returned a NULL pointer\n");
		rc = -EINVAL;
		goto exit_free_thds;
	}

	struct pth_workload_context *ctxs;
	rc = setup_ctxs(&cfg, target, &report, &ctxs);
	if (rc != 0) {
		printf("Cannot set up workload contexts\n");
		goto exit_destroy_target;
	}

	int tcount = 0;
	for (tcount = 0; tcount < cfg.thread_num; ++tcount) {
		rc = pthread_create(&thds[tcount], NULL, workload_thread,
				    &ctxs[tcount]);
		if (rc != 0) {
			printf("Cannot create workload thread %d\n", tcount);

			for (int i = 0; i < tcount; ++i)
				pthread_cancel(thds[i]);

			cancelled = true;
			goto exit_join;
		}
	}

exit_join:
	for (int i = 0; i < tcount; ++i)
		pthread_join(thds[i], NULL);

	if (!cancelled) {
		pthread_mutex_lock(&report.mutex);
		printf("Threads: %d\n", tcount);
		printf("Total duration: %llu\n", report.duration);
		pthread_mutex_unlock(&report.mutex);
	}

exit_free_ctxs:
	free(ctxs);
exit_destroy_target:
	pth_bm_target_destroy(target);
exit_free_thds:
	free(thds);
exit:
	if (rc == 0)
		return EXIT_SUCCESS;
	else
		return EXIT_FAILURE;
}
