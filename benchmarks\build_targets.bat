@echo off
REM Build script for new Pentathlon benchmark targets
REM Demonstrates the new make options: benchmark-optimized and lru

setlocal enabledelayedexpansion

set SCRIPT_DIR=%~dp0
set BUILD_DIR=%SCRIPT_DIR%build

REM Parse arguments
set TARGET=
set VERBOSE=false
set JOBS=%NUMBER_OF_PROCESSORS%
if "%JOBS%"=="" set JOBS=4

:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help
if "%~1"=="-v" (
    set VERBOSE=true
    shift
    goto :parse_args
)
if "%~1"=="--verbose" (
    set VERBOSE=true
    shift
    goto :parse_args
)
if "%~1"=="-j" (
    set JOBS=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="benchmark-optimized" (
    set TARGET=%~1
    shift
    goto :parse_args
)
if "%~1"=="lru" (
    set TARGET=%~1
    shift
    goto :parse_args
)
if "%~1"=="benchmark-skiplist" (
    set TARGET=%~1
    shift
    goto :parse_args
)
if "%~1"=="benchmark-compute" (
    set TARGET=%~1
    shift
    goto :parse_args
)
if "%~1"=="benchmark-all" (
    set TARGET=%~1
    shift
    goto :parse_args
)
if "%~1"=="clean" (
    set TARGET=%~1
    shift
    goto :parse_args
)
echo Error: Unknown option: %~1
goto :show_help

:args_done

if "%TARGET%"=="" (
    echo Error: No target specified
    goto :show_help
)

echo === Pentathlon Benchmark Build Targets ===
echo Target: %TARGET%
echo Jobs: %JOBS%
echo Verbose: %VERBOSE%
echo.

REM Clean build
if "%TARGET%"=="clean" (
    echo === Cleaning Build Directory ===
    
    if exist "%BUILD_DIR%" (
        rmdir /s /q "%BUILD_DIR%"
        echo ✓ Build directory cleaned
    ) else (
        echo ⚠ Build directory does not exist
    )
    goto :eof
)

REM Check if CMake is available
where cmake >nul 2>&1
if errorlevel 1 (
    echo Error: CMake not found. Please install CMake 3.20 or later.
    exit /b 1
)

REM Create build directory
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
cd /d "%BUILD_DIR%"

echo === Configuring with CMake ===

REM Configure based on target
if "%TARGET%"=="benchmark-optimized" (
    set CMAKE_ARGS=-DPENTATHLON_LINK_SKIPLIST=ON -DPENTATHLON_LINK_COMPUTE=ON -DPENTATHLON_LINK_LRU=OFF
    set MAKE_TARGET=benchmark-optimized
    set EXECUTABLE=pentathlon-bm-local-optimized.exe
    set DESCRIPTION=Optimized benchmark (libskiplist_optimized.a + libdm_compiler_rt_compute.a)
) else if "%TARGET%"=="lru" (
    set CMAKE_ARGS=-DPENTATHLON_LINK_SKIPLIST=ON -DPENTATHLON_LINK_COMPUTE=OFF -DPENTATHLON_LINK_LRU=ON
    set MAKE_TARGET=lru
    set EXECUTABLE=pentathlon-bm-local-lru.exe
    set DESCRIPTION=LRU benchmark (libskiplist_optimized.a + lru-cache)
) else if "%TARGET%"=="benchmark-skiplist" (
    set CMAKE_ARGS=-DPENTATHLON_LINK_SKIPLIST=ON -DPENTATHLON_LINK_COMPUTE=OFF -DPENTATHLON_LINK_LRU=OFF
    set MAKE_TARGET=benchmark-skiplist
    set EXECUTABLE=pentathlon-bm-local.exe
    set DESCRIPTION=Skiplist only benchmark
) else if "%TARGET%"=="benchmark-compute" (
    set CMAKE_ARGS=-DPENTATHLON_LINK_SKIPLIST=OFF -DPENTATHLON_LINK_COMPUTE=ON -DPENTATHLON_LINK_LRU=OFF
    set MAKE_TARGET=benchmark-compute
    set EXECUTABLE=pentathlon-bm-local.exe
    set DESCRIPTION=Compute runtime only benchmark
) else if "%TARGET%"=="benchmark-all" (
    set CMAKE_ARGS=-DPENTATHLON_LINK_SKIPLIST=ON -DPENTATHLON_LINK_COMPUTE=ON -DPENTATHLON_LINK_LRU=ON
    set MAKE_TARGET=benchmark-all
    set EXECUTABLE=pentathlon-bm-local.exe
    set DESCRIPTION=All components benchmark
) else (
    echo Error: Unknown target: %TARGET%
    exit /b 1
)

echo Building: !DESCRIPTION!
echo.

REM Configure
if "%VERBOSE%"=="true" (
    echo CMake args: !CMAKE_ARGS!
    cmake !CMAKE_ARGS! "%SCRIPT_DIR%"
) else (
    cmake !CMAKE_ARGS! "%SCRIPT_DIR%" >nul 2>&1
)

if errorlevel 1 (
    echo Error: Configuration failed
    exit /b 1
)

echo ✓ Configuration completed

REM Build
echo Building target '!MAKE_TARGET!' with %JOBS% jobs...
if "%VERBOSE%"=="true" (
    cmake --build . --target !MAKE_TARGET! --config Release -j %JOBS%
) else (
    cmake --build . --target !MAKE_TARGET! --config Release -j %JOBS% >nul 2>&1
)

if errorlevel 1 (
    echo Error: Build failed
    exit /b 1
)

echo ✓ Build completed

REM Check executable
if exist "!EXECUTABLE!" (
    for %%f in ("!EXECUTABLE!") do set SIZE=%%~zf
    echo Executable: %BUILD_DIR%\!EXECUTABLE! (!SIZE! bytes)
) else if exist "Release\!EXECUTABLE!" (
    for %%f in ("Release\!EXECUTABLE!") do set SIZE=%%~zf
    echo Executable: %BUILD_DIR%\Release\!EXECUTABLE! (!SIZE! bytes)
    set EXECUTABLE=Release\!EXECUTABLE!
) else (
    echo ⚠ Executable not found: !EXECUTABLE!
)

cd /d "%SCRIPT_DIR%"

echo.
echo === Build Summary ===
echo Target: %TARGET%
echo Description: !DESCRIPTION!
echo Build directory: %BUILD_DIR%
echo Executable: %BUILD_DIR%\!EXECUTABLE!
echo.
echo To run the benchmark:
echo   "%BUILD_DIR%\!EXECUTABLE!" [options]
echo.
echo Available targets:
echo   benchmark-optimized  - libskiplist_optimized.a + libdm_compiler_rt_compute.a
echo   lru                  - libskiplist_optimized.a + lru-cache
echo   benchmark-skiplist   - Skiplist only
echo   benchmark-compute    - Compute runtime only
echo   benchmark-all        - All components

echo ✓ Build script completed successfully!
goto :eof

:show_help
echo Usage: %~nx0 [TARGET] [OPTIONS]
echo.
echo New Make Targets:
echo   benchmark-optimized  - Links with libskiplist_optimized.a + libdm_compiler_rt_compute.a
echo   lru                  - Links with libskiplist_optimized.a + lru-cache
echo.
echo Other Targets:
echo   benchmark-skiplist   - Skiplist only
echo   benchmark-compute    - Compute runtime only
echo   benchmark-all        - All components
echo   clean                - Clean build directory
echo.
echo Options:
echo   -h, --help           - Show this help
echo   -v, --verbose        - Verbose output
echo   -j N                 - Number of parallel jobs
echo.
echo Examples:
echo   %~nx0 benchmark-optimized
echo   %~nx0 lru -j 8
echo   %~nx0 benchmark-all --verbose
exit /b 0
