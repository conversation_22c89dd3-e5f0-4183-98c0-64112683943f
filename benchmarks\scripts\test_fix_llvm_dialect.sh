#!/bin/bash

# Test script for the LLVM dialect fixer
# Creates a sample LLVM dialect file with problematic patterns and tests the fixer

set -e

# Configuration
TEST_DIR="./test_output"
SAMPLE_FILE="$TEST_DIR/sample_llvm_dialect.mlir"
FIXED_FILE="$TEST_DIR/sample_llvm_dialect_fixed.mlir"

# Create test directory
mkdir -p "$TEST_DIR"

echo "=== LLVM Dialect Fixer Test ==="
echo "Creating sample LLVM dialect file with problematic patterns..."

# Create a sample LLVM dialect file with various patterns
cat > "$SAMPLE_FILE" << 'EOF'
module {
  func.func @test_function(%arg0: !llvm.ptr<i32>) -> i32 {
    // This should be fixed: empty brackets
    %0 = llvm.getelement %arg0[] : (!llvm.ptr<i32>) -> !llvm.ptr<i32>
    
    // This should remain unchanged: already has [0]
    %1 = llvm.getelement %arg0[0] : (!llvm.ptr<i32>) -> !llvm.ptr<i32>
    
    // This should be fixed: empty brackets with complex type
    %2 = llvm.getelement %arg0[] : (!llvm.ptr<struct<(i32, i64)>>) -> !llvm.ptr<i32>
    
    // This should remain unchanged: has index
    %3 = llvm.getelement %arg0[1] : (!llvm.ptr<i32>) -> !llvm.ptr<i32>
    
    // Multiple empty brackets on same line (edge case)
    %4 = llvm.getelement %arg0[] : (!llvm.ptr<i32>) -> !llvm.ptr<i32>
    %5 = llvm.getelement %arg0[] : (!llvm.ptr<i32>) -> !llvm.ptr<i32>
    
    // Complex nested structure with empty brackets
    %6 = llvm.getelement %arg0[] : (!llvm.ptr<array<10 x struct<(i32, f64)>>>) -> !llvm.ptr<struct<(i32, f64)>>
    
    // Normal LLVM operations (should not be affected)
    %7 = llvm.load %1 : !llvm.ptr<i32>
    %8 = llvm.add %7, %7 : i32
    
    llvm.return %8 : i32
  }
  
  func.func @another_function(%arg0: !llvm.ptr<f32>) {
    // More patterns to fix
    %0 = llvm.getelement %arg0[] : (!llvm.ptr<f32>) -> !llvm.ptr<f32>
    %1 = llvm.getelement %arg0[] : (!llvm.ptr<array<5 x f32>>) -> !llvm.ptr<f32>
    
    llvm.return
  }
}
EOF

echo "✓ Sample file created: $SAMPLE_FILE"
echo

# Show the original file content
echo "Original file content:"
echo "======================"
cat "$SAMPLE_FILE"
echo "======================"
echo

# Count patterns before fixing
EMPTY_BRACKETS_BEFORE=$(grep -c "llvm\.getelement.*\[\]" "$SAMPLE_FILE" || true)
ZERO_BRACKETS_BEFORE=$(grep -c "llvm\.getelement.*\[0\]" "$SAMPLE_FILE" || true)

echo "Before fixing:"
echo "  - Empty brackets []: $EMPTY_BRACKETS_BEFORE"
echo "  - Zero brackets [0]: $ZERO_BRACKETS_BEFORE"
echo

# Run the fixer
echo "Running LLVM dialect fixer..."
SCRIPT_DIR="$(dirname "$0")"
if [[ -f "$SCRIPT_DIR/fix_llvm_dialect.sh" ]]; then
    bash "$SCRIPT_DIR/fix_llvm_dialect.sh" -i "$SAMPLE_FILE" -o "$FIXED_FILE" -v -b
else
    echo "Error: fix_llvm_dialect.sh not found in $SCRIPT_DIR"
    exit 1
fi

echo
echo "Fixed file content:"
echo "==================="
cat "$FIXED_FILE"
echo "==================="
echo

# Count patterns after fixing
EMPTY_BRACKETS_AFTER=$(grep -c "llvm\.getelement.*\[\]" "$FIXED_FILE" || true)
ZERO_BRACKETS_AFTER=$(grep -c "llvm\.getelement.*\[0\]" "$FIXED_FILE" || true)

echo "After fixing:"
echo "  - Empty brackets []: $EMPTY_BRACKETS_AFTER"
echo "  - Zero brackets [0]: $ZERO_BRACKETS_AFTER"
echo

# Verify the fix worked correctly
if [[ $EMPTY_BRACKETS_AFTER -eq 0 ]] && [[ $ZERO_BRACKETS_AFTER -gt $ZERO_BRACKETS_BEFORE ]]; then
    echo "✓ Test PASSED: All empty brackets were successfully replaced with [0]"
    
    # Show the differences
    echo
    echo "Differences (- original, + fixed):"
    echo "=================================="
    diff -u "$SAMPLE_FILE" "$FIXED_FILE" || true
    
else
    echo "✗ Test FAILED: Fix did not work as expected"
    echo "Expected: 0 empty brackets, got: $EMPTY_BRACKETS_AFTER"
    exit 1
fi

echo
echo "=== Test Complete ==="
echo "Test files created in: $TEST_DIR"
echo "  - Original: $SAMPLE_FILE"
echo "  - Fixed: $FIXED_FILE"
echo "  - Backup: ${SAMPLE_FILE%.*}_backup.mlir (if created)"
