#include <algorithm>
#include <atomic>
#include <cstdint>
#include <stdexcept>
#include <mutex>
#include <shared_mutex>

#include "addr.h"
#include "common/handshake.h"
#include "data-manager.hpp"

void DataManager::disaggFree(GlobalAddr gaddr) {
    // Implementation of disaggregated free
    // This function should handle the freeing of the memory associated with the given global
    // address It should also update the cache and any necessary data structures Check if the
    // address is valid
    if (!gaddr.global_tag) {
        // Invalid address
        fprintf(stderr, "Error: Invalid address\n");
        return;
    }

    auto metadata = reinterpret_cast<MetaData*>(gaddr.metadata_addr);
    if (metadata == nullptr) {
        // Address not found in the address table
        fprintf(stderr, "Error: Address not found in address table\n");
        return;
    }

    std::unique_lock<std::shared_mutex> lock(metadata->mutex);

    for (auto child : metadata->children) {
        if (child == nullptr) {
            continue;
        }
        std::unique_lock<std::shared_mutex> child_lock(child->mutex);
        child->parents.erase(metadata);
        child_lock.unlock();
    }

    if (metadata->list_node != nullptr) {
        cache_leaf->remove(metadata->list_node);
    }

    for (auto parent : metadata->parents) {
        if (parent == nullptr) {
            continue;
        }
        std::unique_lock<std::shared_mutex> parent_lock(parent->mutex);
        parent->children.erase(metadata);
        if (metadata->local_addr != nullptr) {
            parent->cache_children_num--;
            if (parent->cache_children_num == 0) {
                parent->list_node = new ListNode(parent);
                cache_leaf->push_back(parent->list_node);
            }
        }
        parent_lock.unlock();
    }

    if (metadata->local_addr != nullptr) {
        cache_size.fetch_sub(metadata->size);
        free(metadata->local_addr);
    }
    delete metadata->list_node;
    lock.unlock();
    delete metadata;
}

GlobalAddr DataManager::disaggAlloc(size_t size) {
    // Implementation of disaggregated allocation
    // This function should handle the allocation of memory of the given size
    // It should return a global address that represents the allocated memory
    GlobalAddr addr = GlobalAddr::null();

    // Access chunk_next_addrs with proper synchronization
    uint64_t remote_addr = 0;
    chunk_mutex[size].lock();
    if (next_chunk_addr[size] == 0 || (next_chunk_addr[size] & (chunk_size() - 1)) + size > chunk_size()) {
        next_chunk_addr[size] = chunk_addr.fetch_add(chunk_size());
    }
    remote_addr = next_chunk_addr[size];
    next_chunk_addr[size] += size;
    chunk_mutex[size].unlock();

    if (remote_addr >= mem_upper_bound(rdma->mem())) {
        throw std::runtime_error("out of memory in memory side");
    }

    auto metadata = new MetaData();
    if (metadata == nullptr) {
        throw std::runtime_error("out of memory in compute side");
    }

    addr.global_tag = 1;
    addr.offset = 0;
    addr.metadata_addr = reinterpret_cast<uint64_t>(metadata);

    metadata->size = size;
    metadata->remote_addr = reinterpret_cast<void*>(remote_addr);
    metadata->dirty = true;

    cacheInsert(addr);
    return addr;
}

void DataManager::addAddrDep(GlobalAddr addr_u, GlobalAddr addr_v) {
    if (!addr_u.global_tag || !addr_v.global_tag) {
        return;
    }

    auto metadata_u = reinterpret_cast<MetaData*>(addr_u.metadata_addr);
    auto metadata_v = reinterpret_cast<MetaData*>(addr_v.metadata_addr);
    if (metadata_u == nullptr || metadata_v == nullptr) {
        return;
    }
    if (metadata_u == metadata_v) {
        return;
    }
    std::unique_lock<std::shared_mutex> lock(metadata_u->mutex);
    metadata_u->vchildren[addr_u.offset] = metadata_v;
    lock.unlock();
}

void DataManager::acceptAddrDep(GlobalAddr addr) {
    // Implementation of accepting address dependency
    // This function should handle the acceptance of a dependency for a given global address

    if (!addr.global_tag) {
        return;
    }

    auto metadata = reinterpret_cast<MetaData*>(addr.metadata_addr);
    std::unique_lock lock(metadata->mutex);

    if (metadata->vchildren.count(addr.offset) == 0) {
        lock.unlock();
        return;
    }

    auto metadata_v = metadata->vchildren[addr.offset];
    std::unique_lock lock_v(metadata_v->mutex);

    if (metadata_v->children.count(metadata) > 0) {
        lock.unlock();
        lock_v.unlock();
        return;
    }

    if (metadata->children.count(metadata_v) > 0) {
        lock.unlock();
        lock_v.unlock();
        return;
    }

    metadata->children.insert(metadata_v);
    metadata_v->parents.insert(metadata);
    if (metadata_v->local_addr != nullptr) {
        metadata->cache_children_num++;
        if (metadata->cache_children_num == 1) {
            if (cache_leaf == nullptr) {
                throw std::runtime_error("cache leaf not found");
            }
            cache_leaf->remove(metadata->list_node);  // Remove from leaf if it has children
            delete metadata->list_node;
            metadata->list_node = nullptr;
        }
    }
    lock.unlock();
    lock_v.unlock();
}

void* DataManager::getLocalAddr(GlobalAddr gaddr) {
    // Implementation of getting local address
    // This function should return the local address associated with the given global address
    if (!gaddr.global_tag) {
        return nullptr;
    }

    auto metadata = reinterpret_cast<MetaData*>(gaddr.metadata_addr);
    std::shared_lock<std::shared_mutex> lock(metadata->mutex);
    if (metadata->local_addr == nullptr) {
        lock.unlock();
        return nullptr;
    }
    auto local_addr = metadata->local_addr;
    size_t offset = gaddr.offset;
    if (offset >= metadata->size) {
        return nullptr;
    }
    lock.unlock();
    return (void*)((char*)local_addr + offset);
}

bool DataManager::cacheInsert(GlobalAddr gaddr) {
    // Implementation of cache insertion
    // This function should handle the insertion of a global address into the cache
    // Check if the address is already in the cache
    if (!gaddr.global_tag) {
        return false;
    }
    auto metadata = reinterpret_cast<MetaData*>(gaddr.metadata_addr);
    if (metadata == nullptr) {
        return false;
    }
    std::unique_lock<std::shared_mutex> lock(metadata->mutex);
    if (metadata->local_addr) {
        metadata->freq++;
        lock.unlock();
        return true;
    }

    for (const auto& parent : metadata->parents) {
        if (parent == nullptr) {
            continue;
        }
        std::unique_lock<std::shared_mutex> parent_lock(parent->mutex);
        parent->cache_children_num++;
        if (parent->cache_children_num == 1) {
            if (cache_leaf == nullptr) {
                throw std::runtime_error("cache leaf not found");
            }
            cache_leaf->remove(parent->list_node);  // Remove from leaf if it has children
            delete parent->list_node;
            parent->list_node = nullptr;
        }
        parent_lock.unlock();
    }

    // Insert the address into the cache
    auto size = metadata->size;
    // Cache is full, need to evict an address
    if (cache_leaf == nullptr) {
        throw std::runtime_error("cache leaf not found");
    }
    // Try to evict larger items to make space
    while (is_cache_full(size) && cache_leaf->get_size() > 0) {
        auto evict_node = cache_leaf->pop_front()->metadata;
        if (evict_node == nullptr) {
            throw std::runtime_error("evict node not found");
        }

        std::unique_lock<std::shared_mutex> lock(evict_node->mutex);
        if (evict_node->cache_children_num > 0) {
            continue;  // Address has children, cannot evict, try next
        }

        if (evict_node->local_addr == nullptr) {
            continue;  // Address has no local addr, try next
        }

        cache_size.fetch_sub(evict_node->size);  // Decrease cache size
        if (evict_node->dirty) {
            rdma->write(evict_node->local_addr, evict_node->remote_addr, evict_node->size);  // Write back to memory side
        }

        free(evict_node->local_addr);
        evict_node->local_addr = nullptr;
        delete evict_node->list_node;
        evict_node->list_node = nullptr;
        for (const auto& parent : evict_node->parents) {
            if (parent == nullptr) {
                continue;
            }
            std::unique_lock<std::shared_mutex> parent_lock(parent->mutex);
            parent->cache_children_num--;
            if (parent->cache_children_num == 0) {
                parent->list_node = new ListNode(parent);
                cache_leaf->push_back(parent->list_node);
            }
        }
        lock.unlock();
    }

    if (!is_cache_full(size)) {
        // Cache is not full, insert the address
        cache_size.fetch_add(size);
        void* local_addr = malloc(size);  // Allocate memory for the local address
        if (local_addr == nullptr) {
            cache_size.fetch_sub(size);  // Rollback cache size
            return false;  // Memory allocation failed
        }
        metadata->local_addr = local_addr;
        metadata->freq++;
        metadata->list_node = new ListNode(metadata);
        cache_leaf->push_back(metadata->list_node);
        rdma->read(metadata->remote_addr, local_addr, size);  // Read from memory side
        return true;
    } else {
        return false;
    }
}
