#include <algorithm>
#include <atomic>
#include <cstdint>
#include <stdexcept>
#include <mutex>

#include "addr.h"
#include "common/handshake.h"
#include "data-manager.hpp"

void DataManager::disaggFree(GlobalAddr gaddr) {
    // Implementation of disaggregated free
    // This function should handle the freeing of the memory associated with the given global
    // address It should also update the cache and any necessary data structures Check if the
    // address is valid
    if (gaddr == GlobalAddr::null()) {
        // Invalid address
        fprintf(stderr, "Error: Invalid address\n");
        return;
    }

    NodeType* node = nullptr;
    {
        std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
        auto it = addr_table.find(gaddr);
        if (it == addr_table.end()) {
            // Address not found in the cache
            fprintf(stderr, "Error: Address not found in address table\n");
            return;
        }
        node = it->second;
        if (node == nullptr) {
            // Address not found in the address table
            fprintf(stderr, "Error: Address not found in address table\n");
            return;
        }
    }

    // Collect information from node while holding lock
    void* local_addr_to_free = nullptr;
    size_t size_to_free = 0;
    std::vector<GlobalAddr> children_to_update;
    std::vector<GlobalAddr> parents_to_update;

    {
        std::lock_guard<std::mutex> node_lock(node->mutex);
        void* local_addr = node->local_addr;
        if (local_addr == nullptr) {
            // This is problematic - we can't call cacheInsert while holding node lock
            // as it may cause deadlock. For now, just return error.
            fprintf(stderr, "Error: Local address not found and cannot cache during free\n");
            return;
        }
        // Collect children and parents to update (avoid holding multiple locks)
        children_to_update.assign(node->children.begin(), node->children.end());
        parents_to_update.assign(node->parents.begin(), node->parents.end());

        // Store local_addr before releasing node lock
        local_addr_to_free = node->local_addr;
        size_to_free = get_size(gaddr);
    }

    // Update children (remove this node as parent)
    for (const auto& child : children_to_update) {
        std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
        auto it = addr_table.find(child);
        if (it != addr_table.end() && it->second != nullptr) {
            std::lock_guard<std::mutex> child_lock(it->second->mutex);
            it->second->parents.remove(gaddr);
        }
    }

    // Update parents (remove this node as child)
    for (const auto& parent : parents_to_update) {
        std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
        auto it = addr_table.find(parent);
        if (it != addr_table.end() && it->second != nullptr) {
            std::lock_guard<std::mutex> parent_lock(it->second->mutex);
            it->second->children.remove(gaddr);
            it->second->cache_children_num--;
            if (it->second->cache_children_num == 0) {
                std::lock_guard<std::mutex> cache_lock(cache_leaf_mutex);
                cache_leaf[parent.typeID].push_back(parent);
            }
        }
    }

    // Remove from address table and free node
    {
        std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
        addr_table.erase(gaddr);
    }

    // Now safe to free resources
    delete node;  // Use delete since we used new
    free(local_addr_to_free);  // Free the local address
    // TODO: Free on memory side
    // This is currently no-op
    cache_size -= size_to_free;  // Decrease cache size atomically
}

// LZL: Let's assume each time malloc() allocates exactly one such structure
// Probably should check the passed size with type size in compiler, if not matching then ignore it
GlobalAddr DataManager::disaggAlloc(size_t size) {
    // Implementation of disaggregated allocation
    // This function should handle the allocation of memory of the given size
    // It should return a global address that represents the allocated memory
    GlobalAddr gaddr = GlobalAddr::null();
    gaddr.typeID = get_type_id(size);

    // Access chunk_next_addrs with proper synchronization
    uint64_t allocated_offset;
    {
        std::lock_guard<std::mutex> type_lock(type_mutex);
        auto& chunk_next_addrs = types[gaddr.typeID].chunk_next_addrs;

        // Check if there's still enough room in existing chunk
        if (chunk_next_addrs.empty() ||
            (chunk_next_addrs.back() & (chunk_size() - 1)) + size > chunk_size()) {
            // if not, get new chunk and allocate from it
            uint64_t addr = next_chunk_addr.fetch_add(chunk_size());
            chunk_next_addrs.push_back(addr);
            if (addr >= mem_upper_bound(rdma->mem()))
                throw std::runtime_error("out of memory in memory side");
        }

        allocated_offset = chunk_next_addrs.back();
        chunk_next_addrs.back() += size;
    }

    gaddr.offset = allocated_offset;

    // Allocate memory for the node
    auto node = new NodeType();

    // Insert into address table
    {
        std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
        addr_table[gaddr] = node;
    }

    cacheInsert(gaddr);        // Insert the address into the cache
    return gaddr;
}

void DataManager::addAddrDep(GlobalAddr addr_u, GlobalAddr addr_v) {
    // Implementation of adding address dependency
    // This function should handle the addition of a dependency between two global addresses
    std::lock_guard<std::mutex> dep_lock(acc_dep_mutex);
    temp_access_dep[addr_u] = addr_v;  // Store the dependency
}

void DataManager::acceptAddrDep(GlobalAddr addr) {
    // Implementation of accepting address dependency
    // This function should handle the acceptance of a dependency for a given global address

    GlobalAddr addr_v, addr_u;
    bool has_dependency = false;

    // First, check and extract dependency info
    {
        std::lock_guard<std::mutex> dep_lock(acc_dep_mutex);
        auto it = temp_access_dep.find(addr);
        if (it != temp_access_dep.end()) {
            addr_v = get_orig_global_addr(it->second);
            addr_u = get_orig_global_addr(addr);
            temp_access_dep.erase(it);
            has_dependency = true;
        }
    }

    if (!has_dependency) {
        return;
    }

    // Get node pointers safely
    NodeType* node_v = nullptr;
    NodeType* node_u = nullptr;
    {
        std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
        auto it_v = addr_table.find(addr_v);
        auto it_u = addr_table.find(addr_u);
        if (it_v != addr_table.end()) node_v = it_v->second;
        if (it_u != addr_table.end()) node_u = it_u->second;
    }

    if (node_v == nullptr || node_u == nullptr) {
        return;  // One of the addresses is not in the address table
    }

    // Check if dependency already exists and add if not
    // Lock in consistent order to prevent deadlock: always lock lower address first
    NodeType* first_node = (addr_u < addr_v) ? node_u : node_v;
    NodeType* second_node = (addr_u < addr_v) ? node_v : node_u;

    std::lock_guard<std::mutex> first_lock(first_node->mutex);
    std::lock_guard<std::mutex> second_lock(second_node->mutex);

    // Check if v is already u's parent
    if (std::find(node_u->parents.begin(), node_u->parents.end(), addr_v) != node_u->parents.end()) {
        return;  // addr_v is already a parent of addr_u
    }

    // Add u -> v dependency
    node_v->parents.push_back(addr_u);  // Add addr_u as a parent of addr_v
    node_u->children.push_back(addr_v);  // Add addr_v as a child of addr_u

    if (node_v->local_addr != nullptr) {
        node_u->cache_children_num++;
        if (node_u->cache_children_num == 1) {
            std::lock_guard<std::mutex> cache_lock(cache_leaf_mutex);
            cache_leaf[addr_u.typeID].remove(addr_u);  // Remove addr_u from the leaf
        }
    }
}

void* DataManager::getLocalAddr(GlobalAddr gaddr) {
    // Implementation of getting local address
    // This function should return the local address associated with the given global address
    auto offset = get_offset(gaddr);
    gaddr = get_orig_global_addr(gaddr);

    std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
    auto it = addr_table.find(gaddr);
    if (it != addr_table.end()) {
        auto node = it->second;
        if (node == nullptr) {
            return nullptr;  // Address not found in the address table
        }
        std::lock_guard<std::mutex> node_lock(node->mutex);
        auto local_addr = node->local_addr;
        return local_addr ? (static_cast<char*>(local_addr) + offset) : nullptr;
    }
    return nullptr;  // Address not found
}

bool DataManager::cacheInsert(GlobalAddr gaddr) {
    // Implementation of cache insertion
    // This function should handle the insertion of a global address into the cache
    // Check if the address is already in the cache
    gaddr = get_orig_global_addr(gaddr);

    NodeType* node = nullptr;
    {
        std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
        auto it = addr_table.find(gaddr);
        if (it == addr_table.end()) {
            return false;  // Address not found in the address table
        }
        node = it->second;
    }

    if (node == nullptr) {
        return false;  // Address not found in the address table
    }

    auto local_addr = getLocalAddr(gaddr);
    if (local_addr) {
        // Address is already in cache
        std::lock_guard<std::mutex> node_lock(node->mutex);
        node->freq++;  // Increase the frequency of the address
        return true;
    }

    // Insert the address into the cache
    auto size = get_size(gaddr);
    if (!is_cache_full(size)) {
        // Cache is not full, insert the address
        cache_size += size;
        local_addr = malloc(size);  // Allocate memory for the local address
        if (local_addr == nullptr) {
            cache_size -= size;  // Rollback cache size
            return false;  // Memory allocation failed
        }
        {
            std::lock_guard<std::mutex> node_lock(node->mutex);
            node->freq++;  // Increase the frequency of the address
            node->local_addr = local_addr;  // Allocate memory for the local address
            rdma->read(gaddr, local_addr, size); // Read from memory side
        }
        update_cache_leaf_insert(gaddr);  // Update the cache leaf
        return true;
    }
    // Cache is full, need to evict an address
    GlobalAddr evict_addr;
    bool found_evictable = false;

    {
        std::lock_guard<std::mutex> cache_lock(cache_leaf_mutex);
        if (!cache_leaf[gaddr.typeID].empty()) {
            evict_addr = cache_leaf[gaddr.typeID].front();
            found_evictable = true;
        }
    }

    if (found_evictable) {
        NodeType* evict_node = nullptr;
        {
            std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
            auto it = addr_table.find(evict_addr);
            if (it != addr_table.end()) {
                evict_node = it->second;
            }
        }

        if (evict_node == nullptr) {
            return false;  // Address not found in the address table
        }

        void* evicted_local_addr = nullptr;
        {
            std::lock_guard<std::mutex> evict_lock(evict_node->mutex);
            // Check if the evicted address has children
            if (evict_node->cache_children_num > 0) {
                return false;  // Address has children, cannot evict
            }
            // Evict the address from the cache
            evicted_local_addr = evict_node->local_addr;
            if (!evicted_local_addr) {
                return false;  // Local address not found
            }
            rdma->write(evicted_local_addr, evict_addr, get_size(evict_addr));  // Write back to memory side
            evict_node->local_addr = nullptr;  // Remove the local address
        }

        update_cache_leaf_remove(evict_addr);  // Update the cache leaf

        {
            std::lock_guard<std::mutex> node_lock(node->mutex);
            node->local_addr = evicted_local_addr;  // Assign the evicted address to the new address
            node->freq++;  // Increase the frequency of the address
            rdma->read(gaddr, evicted_local_addr, size);  // read from memory side
        }
        update_cache_leaf_insert(gaddr);  // Update the cache leaf
        return true;
    }

    // Try to evict larger items to make space
    while (is_cache_full(size)) {
        GlobalAddr evict_addr;
        size_t evict_size = 0;
        bool found_evictable = false;

        {
            std::lock_guard<std::mutex> cache_lock(cache_leaf_mutex);
            for (size_t i = 0; i < cache_leaf.size(); i++) {
                if (cache_leaf[i].empty()) {
                    continue;
                }
                auto addr = cache_leaf[i].front();
                size_t addr_size = get_size(addr);
                if (addr_size > evict_size) {
                    evict_size = addr_size;
                    evict_addr = addr;  // Find the largest address to evict
                    found_evictable = true;
                }
            }
        }

        if (!found_evictable || evict_size == 0) {
            return false;  // No address to evict
        }

        NodeType* evict_node = nullptr;
        {
            std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
            auto it = addr_table.find(evict_addr);
            if (it != addr_table.end()) {
                evict_node = it->second;
            }
        }

        if (evict_node == nullptr) {
            return false;  // Address not found in the address table
        }

        void* evicted_local_addr = nullptr;
        bool can_evict = false;
        {
            std::lock_guard<std::mutex> evict_lock(evict_node->mutex);
            // Check if the evicted address has children
            if (evict_node->cache_children_num == 0) {
                evicted_local_addr = evict_node->local_addr;
                if (evicted_local_addr) {
                    evict_node->local_addr = nullptr;  // Remove the local address
                    can_evict = true;
                }
            }
        }

        if (!can_evict) {
            continue;  // Address has children or no local addr, cannot evict, try next
        }

        rdma->write(evicted_local_addr, evict_addr, evict_size);  // Write back to memory side
        free(evicted_local_addr);  // Free the evicted address
        update_cache_leaf_remove(evict_addr);  // Update the cache leaf
        cache_size -= evict_size;  // Decrease cache size
    }

    if (!is_cache_full(size)) {
        // Cache is not full, insert the address
        cache_size += size;
        local_addr = malloc(size);  // Allocate memory for the local address
        if (local_addr == nullptr) {
            cache_size -= size;  // Rollback cache size
            return false;  // Memory allocation failed
        }
        {
            std::lock_guard<std::mutex> node_lock(node->mutex);
            node->freq++;  // Increase the frequency of the address
            node->local_addr = local_addr;  // Allocate memory for the local address
            rdma->read(gaddr, local_addr, size);  // read from memory side
        }
        update_cache_leaf_insert(gaddr);  // Update the cache leaf
        return true;
    }
    return false;
}
