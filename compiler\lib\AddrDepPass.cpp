#include "AddrDepPass.h"

#include "mlir/IR/BuiltinOps.h"
#include "mlir/IR/Builders.h"
#include "mlir/Pass/Pass.h"
#include "mlir/Dialect/LLVMIR/LLVMDialect.h"

using namespace mlir;

namespace {
struct AddrDepPass : public PassWrapper<AddrDepPass, OperationPass<ModuleOp>> {
	void runOnOperation() override {
		ModuleOp module = getOperation();
		LLVM::LLVMFuncOp addAddrDepFunc = ensureAddAddrDepFunc(module);
		LLVM::LLVMFuncOp acceptAddrDepFunc = ensureAcceptAddrDepFunc(module);

		module.walk([&](LLVM::StoreOp storeOp) {
			Value value = storeOp.getValue();
			Value addr = storeOp.getAddr();

			// Check both operands are pointers
			if (!value.getType().isa<LLVM::LLVMPointerType>())
				return;
			if (!addr.getType().isa<LLVM::LLVMPointerType>())
				return;

			OpBuilder builder(storeOp);
			builder.create<LLVM::CallOp>(storeOp.getLoc(), addAddrDepFunc, ValueRange{addr, value});
		});
		module.walk([&](LLVM::LoadOp loadOp) {

			Value fromAddr = loadOp.getAddr();
			auto fromType = fromAddr.getType().dyn_cast<LLVM::LLVMPointerType>();
			if (!fromType)
				return;

        	// Insert the call before the load
			OpBuilder builder(loadOp);
			builder.create<LLVM::CallOp>(loadOp.getLoc(), acceptAddrDepFunc, ValueRange{fromAddr});
		});
	}

	LLVM::LLVMFuncOp ensureAddAddrDepFunc(ModuleOp module) {
		if (auto f = module.lookupSymbol<LLVM::LLVMFuncOp>("addAddrDep"))
			return f;

		auto *ctx = module.getContext();
		auto voidPtrTy = LLVM::LLVMPointerType::get(ctx);
		auto voidTy = LLVM::LLVMVoidType::get(ctx);
		auto funcTy = LLVM::LLVMFunctionType::get(voidTy, {voidPtrTy, voidPtrTy}, false);

		OpBuilder builder(module.getBodyRegion());
		return builder.create<LLVM::LLVMFuncOp>(
				module.getLoc(), "addAddrDep", funcTy);
	}

	LLVM::LLVMFuncOp ensureAcceptAddrDepFunc(ModuleOp module) {
		if (auto f = module.lookupSymbol<LLVM::LLVMFuncOp>("acceptAddrDep"))
		  return f;

		auto *ctx = module.getContext();
		auto voidTy = LLVM::LLVMVoidType::get(ctx);
		auto voidPtrTy = LLVM::LLVMPointerType::get(ctx);
		auto funcTy = LLVM::LLVMFunctionType::get(voidTy, {voidPtrTy}, false);

		OpBuilder builder(module.getBodyRegion());
		return builder.create<LLVM::LLVMFuncOp>(module.getLoc(), "acceptAddrDep", funcTy);
	}

	StringRef getArgument() const final { return "addr-dep-pass"; }
	StringRef getDescription() const final {
		return "Insert addAddrDep(value, addr) for pointer stores.";
	}
};
} // end anonymous namespace

std::unique_ptr<Pass> createAddrDepPass() {
	return std::make_unique<AddrDepPass>();
}

/// Register the pass
static PassRegistration<AddrDepPass> pass;
