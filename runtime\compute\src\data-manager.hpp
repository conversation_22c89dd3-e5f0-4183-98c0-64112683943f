#ifndef COMPUTE_DATA_MANAGER_H
#define COMPUTE_DATA_MANAGER_H

#include <stdbool.h>
#include <stdint.h>
#include <atomic>
#include <cstddef>
#include <list>
#include <mutex>
#include <shared_mutex>
#include <unordered_map>
#include <vector>
#include <sys/resource.h>
#include <set>

#include "addr.h"
#include "rdma.hpp"

#ifndef COMPUTE_CACHE_LIMIT
#define COMPUTE_CACHE_LIMIT (1024 * 1024 * 1024)  // 1GB
#endif

struct ListNode;
class List;

struct MetaData {
    std::shared_mutex mutex;
    bool dirty;
    size_t freq;
    size_t size;
    void* local_addr;
    void* remote_addr;
    ListNode* list_node;
    size_t cache_children_num;
    std::set<MetaData*> parents;
    std::set<MetaData*> children;
    std::unordered_map<size_t, MetaData*> vchildren;
    MetaData() : dirty(false), freq(0), size(0), local_addr(nullptr), remote_addr(nullptr), cache_children_num(0), 
                 children(), parents(), vchildren() {
        // Initialize the node with default values
    }
};

struct ListNode {
    MetaData* metadata;
    ListNode* next;
    ListNode* prev;
    ListNode(MetaData* metadata) : metadata(metadata), next(nullptr), prev(nullptr) {}
};

class List {
private:
    ListNode* head;
    ListNode* tail;
    size_t size;
    std::shared_mutex mutex;
public:
    List() : head(nullptr), tail(nullptr), size(0), mutex() {}
    void push_back(ListNode* node) {
        std::unique_lock lock(mutex);
        if (node == nullptr) {
            return;
        }
        if (head == nullptr) {
            head = tail = node;
            node->prev = nullptr;
            node->next = nullptr;
        } else {
            tail->next = node;
            node->prev = tail;
            node->next = nullptr;
            tail = node;
        }
        size++;
    }
    void remove(ListNode* node) {
        std::unique_lock lock(mutex);
        if (node == nullptr) {
            return;
        }
        if (node->prev != nullptr) {
            node->prev->next = node->next;
        } else {
            head = node->next;
        }
        if (node->next != nullptr) {
            node->next->prev = node->prev;
        } else {
            tail = node->prev;
        }
        node->prev = nullptr;
        node->next = nullptr;
        size--;
    }
    ListNode* get_head() {
        std::shared_lock lock(mutex);
        return head;
    }
    ListNode* get_tail() {
        std::shared_lock lock(mutex);
        return tail;
    }
    size_t get_size() {
        std::shared_lock lock(mutex);
        return size;
    }
    ListNode* pop_front() {
        std::unique_lock lock(mutex);
        if (head == nullptr) {
            return nullptr;
        }
        auto node = head;
        remove(head);
        return node;
    }
};

class DataManager {
    RDMAClient* rdma;

    std::atomic<uint64_t> chunk_addr;
    std::atomic<size_t> cache_size;
    // Memory allocation
    static constexpr size_t MAX_SIZE = 1 << 15;
    // max size: 32KB
    std::mutex chunk_mutex[MAX_SIZE + 1];
    uint64_t next_chunk_addr[MAX_SIZE + 1];
    List* cache_leaf;

    size_t chunk_size() { return rdma->mem().page_size; }


    inline bool is_cache_full(size_t size) {
        // Check if the cache size exceeds the limit
        return cache_size.load() + size > COMPUTE_CACHE_LIMIT;
    }

   public:
    // std::mutex mutex;  // let's make it public for now

    void disaggFree(GlobalAddr gaddr);
    GlobalAddr disaggAlloc(size_t size);
    void addAddrDep(GlobalAddr addr_u, GlobalAddr addr_v);  // u -> v
    void acceptAddrDep(GlobalAddr addr);
    void* getLocalAddr(GlobalAddr gaddr);
    bool cacheInsert(GlobalAddr gaddr);

    DataManager(RDMAClient* rdma) : rdma(rdma)  {
        // Initialize the data manager
        chunk_addr.store(rdma->mem().addr);
        cache_size.store(0);
        for (size_t i = 0; i <= MAX_SIZE; i++) {
            next_chunk_addr[i] = 0;
            // overload resolution selected deleted operator '='
            // chunk_mutex[i] = std::mutex();
            new (&chunk_mutex[i]) std::mutex();
        }
        cache_leaf = new List();
    }
};

#endif
