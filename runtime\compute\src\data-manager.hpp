#ifndef COMPUTE_DATA_MANAGER_H
#define COMPUTE_DATA_MANAGER_H

#include <stdbool.h>
#include <stdint.h>
#include <atomic>
#include <cstddef>
#include <list>
#include <mutex>
#include <unordered_map>
#include <vector>
#include <sys/resource.h>

#include "addr.h"
#include "rdma.hpp"

#ifndef COMPUTE_CACHE_LIMIT
#define COMPUTE_CACHE_LIMIT (1024 * 1024 * 1024)  // 1GB
#endif

struct AddressDep {
    std::list<GlobalAddr> children;
    size_t children_num;
    std::list<GlobalAddr> parents;

    AddressDep() = default;
};

struct NodeType {
    size_t freq;
    std::mutex mutex;  // for thread safety
    void* local_addr;
    std::list<GlobalAddr> children;
    size_t cache_children_num;
    std::list<GlobalAddr> parents;
    NodeType() : freq(0), local_addr(nullptr), cache_children_num(0), 
                 children(), parents(), mutex() {
        // Initialize the node with default values
    }
};

struct DataType {
    size_t size;
    std::vector<uint64_t> chunk_next_addrs;
    DataType(size_t size) : size(size) {}
};

class DataManager {
    RDMAClient* rdma;

    // Memory allocation
    std::mutex type_mutex;
    std::vector<DataType> types;
    std::mutex cache_leaf_mutex;
    std::vector<std::list<GlobalAddr>> cache_leaf;
    std::unordered_map<size_t, uint8_t> size_to_type_id;

    std::mutex addr_table_mutex;
    std::unordered_map<GlobalAddr, NodeType*> addr_table;
    std::mutex acc_dep_mutex;
    std::unordered_map<GlobalAddr, GlobalAddr> temp_access_dep;
    std::atomic_uint64_t next_chunk_addr;
    size_t cache_size = 0;
    size_t chunk_size() { return rdma->mem().page_size; }

    GlobalAddr get_orig_global_addr(GlobalAddr addr) {
        return {.typeID = addr.typeID, .offset = addr.offset - get_offset(addr)};
    }

    size_t get_size(GlobalAddr addr) {
        // Get the size of the data type based on the type ID
        type_mutex.lock();
        if (addr.typeID >= types.size()) {
            type_mutex.unlock();
            throw std::runtime_error("Invalid type ID");
        }
        type_mutex.unlock();
        // get the lock
        type_mutex.lock();
        size_t size = types[addr.typeID].size;
        type_mutex.unlock();
        return size;
    }

    size_t get_offset(GlobalAddr addr) {
        // Get the offset of the data type based on the type ID
        type_mutex.lock();
        if (addr.typeID >= types.size()) {
            type_mutex.unlock();
            throw std::runtime_error("Invalid type ID");
        }
        type_mutex.unlock();
        return (addr.offset % chunk_size()) % get_size(addr);
    }

    uint8_t get_type_id(size_t size) {
        type_mutex.lock();
        if (size_to_type_id.find(size) != size_to_type_id.end()) {
            // If type with the same size exists, reuse that type
            auto index = size_to_type_id[size];
            type_mutex.unlock();
            return index;
        }
        // else, register new type
        auto new_type = DataType(size);
        types.push_back(new_type);
        auto index = types.size() - 1;
        size_to_type_id[size] = index;
        cache_leaf_mutex.lock();
        cache_leaf.push_back(std::list<GlobalAddr>());
        cache_leaf_mutex.unlock();
        type_mutex.unlock();
        return index;
    }
    
    void update_cache_leaf_remove(GlobalAddr gaddr) {
        // Update the cache leaf
        addr_table_mutex.lock();
        auto node = addr_table[gaddr];
        addr_table_mutex.unlock();
        if (node == nullptr) {
            return;  // Address not found in the address table
        }
        node->mutex.lock();
        for (auto& parent : node->parents) {
            addr_table_mutex.lock();
            auto parent_node = addr_table[parent];
            addr_table_mutex.unlock();
            if (parent_node == nullptr) {
                continue;  // Parent not found in the address table
            }
            parent_node->mutex.lock();
            auto& x = parent_node->cache_children_num;
            x--;
            if (x == 0) {
                cache_leaf_mutex.lock();
                cache_leaf[parent.typeID].push_back(parent);  // Add to leaf if no children
                cache_leaf_mutex.unlock();
            }
            parent_node->mutex.unlock();
        }
        node->mutex.unlock();
        cache_leaf_mutex.lock();
        cache_leaf[gaddr.typeID].remove(gaddr);  // Remove from the cache leaf
        cache_leaf_mutex.unlock();
    }

    void update_cache_leaf_insert(GlobalAddr gaddr) {
        // Update the cache leaf
        addr_table_mutex.lock();
        auto node = addr_table[gaddr];
        if (node == nullptr) {
            addr_table_mutex.unlock();
            return;  // Address not found in the address table
        }
        node->mutex.lock();
        addr_table_mutex.unlock();
        for (auto& parent : node->parents) {
            addr_table_mutex.lock();
            auto parent_node = addr_table[parent];
            addr_table_mutex.unlock();
            if (parent_node == nullptr) {
                continue;  // Parent not found in the address table
            }
            parent_node->mutex.lock();
            auto& x = parent_node->cache_children_num;
            x++;
            if (x == 1) {
                cache_leaf_mutex.lock();
                cache_leaf[parent.typeID].remove(parent);  // Remove from leaf if it has children
                cache_leaf_mutex.unlock();
            }
            parent_node->mutex.unlock();
        }
        node->mutex.unlock();  // Unlock the node for thread safety
        cache_leaf_mutex.lock();
        cache_leaf[gaddr.typeID].push_back(gaddr);  // Add to the cache leaf
        cache_leaf_mutex.unlock();
    }

    inline bool is_cache_full(size_t size) {
        // Check if the cache size exceeds the limit
        // return cache_size + size > COMPUTE_CACHE_LIMIT;
        struct rusage usage;
        getrusage(RUSAGE_SELF, &usage);
        size_t mem_usage = usage.ru_maxrss * 1024;  // Convert to bytes
        return mem_usage + size > COMPUTE_CACHE_LIMIT;
    }

   public:
    // std::mutex mutex;  // let's make it public for now

    void disaggFree(GlobalAddr gaddr);
    GlobalAddr disaggAlloc(size_t size);
    void addAddrDep(GlobalAddr addr_u, GlobalAddr addr_v);  // u -> v
    void acceptAddrDep(GlobalAddr addr);
    void* getLocalAddr(GlobalAddr gaddr);
    bool cacheInsert(GlobalAddr gaddr);

    DataManager(RDMAClient* rdma) : rdma(rdma), type_mutex(), types(), cache_leaf_mutex(),
                                   cache_leaf(), size_to_type_id(), addr_table_mutex(),
                                   temp_access_dep(), next_chunk_addr(0), cache_size(0) {
        // Initialize the data manager
        types.push_back(DataType(0));  // 0 is invalid type
        cache_leaf.push_back(std::list<GlobalAddr>());
        next_chunk_addr = rdma->mem().addr;
    }
};

#endif
