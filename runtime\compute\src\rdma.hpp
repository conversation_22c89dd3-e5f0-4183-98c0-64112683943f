#ifndef COMPUTE_RDMA_HPP
#define COMPUTE_RDMA_HPP

#include <infiniband/verbs.h>
#include <cstdint>
#include <stdexcept>

#include "addr.h"
#include "common/rdma.h"
#include "rdma.h"

const size_t CACHE_LINE_SIZE = 64;

// C++ wrapper of C `struct rdma_client`.
class RDMAClient {
    rdma_client* inner;
    void* buf;  // registered memory region
    ibv_mr* buf_mr;

   public:
    RDMAClient(struct sockaddr& addr) {
        inner = rdma_client_connect(&addr);
        if (!inner) throw std::runtime_error("failed to create RDMA client");
        buf = malloc(CACHE_LINE_SIZE);
        buf_mr =
            ibv_reg_mr(inner->conn->pd, buf, CACHE_LINE_SIZE,
                       IBV_ACCESS_LOCAL_WRITE | IBV_ACCESS_REMOTE_READ | IBV_ACCESS_REMOTE_WRITE);
        if (!buf_mr) throw std::runtime_error("failed to register compute memory region");
    }

    ~RDMAClient() { rdma_client_free(inner); }

    memory_info mem() { return inner->mem; }

    void read(GlobalAddr from, void* to, uint32_t size);
    void write(void* from, GlobalAddr to, uint32_t size);
};

#endif  // COMPUTE_RDMA_HPP
