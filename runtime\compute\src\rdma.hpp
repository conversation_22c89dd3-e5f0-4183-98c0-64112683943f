#ifndef COMPUTE_RDMA_HPP
#define COMPUTE_RDMA_HPP

#include <infiniband/verbs.h>
#include <threads.h>
#include <cstdint>
#include <stdexcept>

#include "addr.h"
#include "common/rdma.h"
#include "rdma.h"

const size_t CACHE_LINE_SIZE = 1024 * 64;

// Thread-local buffer for RDMA operations
struct ThreadBuffer {
    void* buf;
    ibv_mr* buf_mr;

    ThreadBuffer(ibv_pd* pd) {
        buf = malloc(CACHE_LINE_SIZE);
        if (!buf) throw std::runtime_error("failed to allocate thread buffer");
        buf_mr =
            ibv_reg_mr(pd, buf, CACHE_LINE_SIZE,
                       IBV_ACCESS_LOCAL_WRITE | IBV_ACCESS_REMOTE_READ | IBV_ACCESS_REMOTE_WRITE);
        if (!buf_mr) {
            free(buf);
            throw std::runtime_error("failed to register thread memory region");
        }
    }

    static ThreadBuffer& get_instance(ibv_pd* pd) {
        static thread_local ThreadBuffer buf = ThreadBuffer(pd);
        return buf;
    }

    ~ThreadBuffer() {
        if (buf_mr) ibv_dereg_mr(buf_mr);
        if (buf) free(buf);
    }
};

// C++ wrapper of C `struct rdma_client` with thread safety.
class RDMAClient {
    rdma_client* inner;

    ThreadBuffer& get_thread_buffer() { return ThreadBuffer::get_instance(inner->conn->pd); }

   public:
    RDMAClient(struct sockaddr& addr) {
        inner = rdma_client_connect(&addr);
        if (!inner) throw std::runtime_error("failed to create RDMA client");
    }

    ~RDMAClient() {
        // Clean up all thread buffers
        rdma_client_free(inner);
    }

    memory_info mem() { return inner->mem; }

    void read(void* from, void* to, uint32_t size);
    void write(void* from, void* to, uint32_t size);
};

#endif  // COMPUTE_RDMA_HPP
