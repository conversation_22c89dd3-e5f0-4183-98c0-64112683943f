cmake_minimum_required(VERSION 3.10)
project(bplus_tree C)

# Set C standard
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Compiler flags
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -g")

# Source files
set(BPTREE_SOURCES
    bplus_tree.c
)

# Header files
set(BPTREE_HEADERS
    bplus_tree.h
)

# Create static library
add_library(pentathlon-bm-target-bptree STATIC ${BPTREE_SOURCES})
target_include_directories(pentathlon-bm-target-bptree PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})

# Main executable
add_executable(bplus_tree main.c)
target_link_libraries(bplus_tree pentathlon-bm-target-bptree pthread)

# Test programs
add_executable(test_simple test_simple.c)
target_link_libraries(test_simple pentathlon-bm-target-bptree pthread)

add_executable(test_split test_split.c)
target_link_libraries(test_split pentathlon-bm-target-bptree pthread)

add_executable(test_concurrent test_concurrent.c)
target_link_libraries(test_concurrent pentathlon-bm-target-bptree pthread)

# Custom targets for testing
add_custom_target(test-simple
    COMMAND ./test_simple
    DEPENDS test_simple
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

add_custom_target(test-split
    COMMAND ./test_split
    DEPENDS test_split
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

add_custom_target(test-concurrent
    COMMAND ./test_concurrent
    DEPENDS test_concurrent
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

add_custom_target(test-all
    DEPENDS test-simple test-split test-concurrent
    COMMAND ./test_simple && ./test_split && ./test_concurrent
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

# Valgrind targets
add_custom_target(valgrind
    COMMAND valgrind --tool=memcheck --leak-check=full ./bplus_tree < /dev/null
    DEPENDS bplus_tree
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

add_custom_target(valgrind-simple
    COMMAND valgrind --tool=memcheck --leak-check=full ./test_simple
    DEPENDS test_simple
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

add_custom_target(valgrind-concurrent
    COMMAND valgrind --tool=helgrind ./test_concurrent
    DEPENDS test_concurrent
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)
