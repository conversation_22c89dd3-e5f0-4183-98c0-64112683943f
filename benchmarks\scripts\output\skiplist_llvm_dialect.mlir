module attributes {dlti.dl_spec = #dlti.dl_spec<#dlti.dl_entry<!llvm.ptr<270>, dense<32> : vector<4xi32>>, #dlti.dl_entry<!llvm.ptr<271>, dense<32> : vector<4xi32>>, #dlti.dl_entry<!llvm.ptr<272>, dense<64> : vector<4xi32>>, #dlti.dl_entry<i32, dense<32> : vector<2xi32>>, #dlti.dl_entry<f64, dense<64> : vector<2xi32>>, #dlti.dl_entry<f128, dense<128> : vector<2xi32>>, #dlti.dl_entry<f16, dense<16> : vector<2xi32>>, #dlti.dl_entry<!llvm.ptr, dense<64> : vector<4xi32>>, #dlti.dl_entry<i1, dense<8> : vector<2xi32>>, #dlti.dl_entry<i16, dense<16> : vector<2xi32>>, #dlti.dl_entry<i8, dense<8> : vector<2xi32>>, #dlti.dl_entry<i64, dense<64> : vector<2xi32>>, #dlti.dl_entry<f80, dense<128> : vector<2xi32>>, #dlti.dl_entry<"dlti.endianness", "little">, #dlti.dl_entry<"dlti.stack_alignment", 128 : i32>>, llvm.data_layout = "e-m:e-p270:32:32-p271:32:32-p272:64:64-i64:64-f80:128-n8:16:32:64-S128", llvm.target_triple = "x86_64-unknown-linux-gnu", "polygeist.target-cpu" = "x86-64", "polygeist.target-features" = "+cmov,+cx8,+fxsr,+mmx,+sse,+sse2,+x87", "polygeist.tune-cpu" = "generic"} {
  llvm.func @free(!llvm.ptr)
  llvm.func @malloc(i64) -> !llvm.ptr
  llvm.mlir.global internal constant @str2("---------- Display done! ----------\0A\0A\00") {addr_space = 0 : i32}
  llvm.mlir.global external @_ZSt4cout() {addr_space = 0 : i32} : !llvm.struct<(ptr, struct<(struct<(ptr, i64, i64, i32, i32, i32, ptr, struct<(ptr, i64)>, array<8 x struct<(ptr, i64)>>, i32, ptr, struct<(ptr)>)>, ptr, i8, i8, ptr, ptr, ptr, ptr)>)>
  llvm.mlir.global internal constant @str1("%d -> \00") {addr_space = 0 : i32}
  llvm.mlir.global internal constant @str0("Level %d  \00") {addr_space = 0 : i32}
  llvm.func @printf(!llvm.ptr, ...) -> i32
  llvm.mlir.global private @_ZL9max_level() {addr_space = 0 : i32} : !llvm.array<1 x i32> {
    %0 = llvm.mlir.undef : !llvm.array<1 x i32>
    llvm.return %0 : !llvm.array<1 x i32>
  }
  llvm.func @_ZN8SkipListC1Eif(%arg0: !llvm.ptr, %arg1: i32, %arg2: f32) {
    %0 = llvm.mlir.constant(1104 : i64) : i64
    %1 = llvm.mlir.constant(1 : index) : i64
    %2 = llvm.mlir.constant(-1 : i32) : i32
    %3 = llvm.mlir.constant(2147483647 : i32) : i32
    %4 = llvm.mlir.constant(-2147483648 : i32) : i32
    %5 = llvm.mlir.constant(1.000000e+00 : f32) : f32
    %6 = llvm.mlir.addressof @_ZL9max_level : !llvm.ptr
    %7 = llvm.sitofp %arg1 : i32 to f64
    %8 = llvm.intr.log(%7)  : (f64) -> f64
    %9 = llvm.fdiv %5, %arg2  : f32
    %10 = llvm.fpext %9 : f32 to f64
    %11 = llvm.intr.log(%10)  : (f64) -> f64
    %12 = llvm.fdiv %8, %11  : f64
    %13 = llvm.call @round(%12) : (f64) -> f64
    %14 = llvm.fptosi %13 : f64 to i32
    %15 = llvm.add %14, %2  : i32
    llvm.store %15, %6 : i32, !llvm.ptr
    %16 = llvm.mul %0, %1  : i64
    %17 = llvm.call @malloc(%16) : (i64) -> !llvm.ptr
    %18 = llvm.mlir.zero : !llvm.ptr
    llvm.call @_ZN4NodeC1EiPKhi(%17, %4, %18, %15) : (!llvm.ptr, i32, !llvm.ptr, i32) -> ()
    llvm.store %17, %arg0 : !llvm.ptr, !llvm.ptr
    %19 = llvm.getelementptr %arg0[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr)>
    %20 = llvm.mul %0, %1  : i64
    %21 = llvm.call @malloc(%20) : (i64) -> !llvm.ptr
    %22 = llvm.load %6 : !llvm.ptr -> i32
    llvm.call @_ZN4NodeC1EiPKhi(%21, %3, %18, %22) : (!llvm.ptr, i32, !llvm.ptr, i32) -> ()
    llvm.store %21, %19 : !llvm.ptr, !llvm.ptr
    %23 = llvm.load %arg0 : !llvm.ptr -> !llvm.ptr
    %24 = llvm.getelementptr %23[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %25 = llvm.load %24 : !llvm.ptr -> !llvm.ptr
    llvm.br ^bb1(%25 : !llvm.ptr)
  ^bb1(%26: !llvm.ptr):  // 2 preds: ^bb0, ^bb2
    %27 = llvm.icmp "ne" %26, %18 : !llvm.ptr
    llvm.cond_br %27, ^bb2(%26 : !llvm.ptr), ^bb3
  ^bb2(%28: !llvm.ptr):  // pred: ^bb1
    %29 = llvm.load %19 : !llvm.ptr -> !llvm.ptr
    llvm.store %29, %28 : !llvm.ptr, !llvm.ptr
    %30 = llvm.getelementptr %28[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr)>
    %31 = llvm.load %30 : !llvm.ptr -> !llvm.ptr
    llvm.br ^bb1(%31 : !llvm.ptr)
  ^bb3:  // pred: ^bb1
    llvm.return
  }
  llvm.func @round(f64) -> f64 attributes {sym_visibility = "private"}
  llvm.func linkonce_odr @_ZN4NodeC1EiPKhi(%arg0: !llvm.ptr, %arg1: i32, %arg2: !llvm.ptr, %arg3: i32) {
    %0 = llvm.mlir.constant(0 : index) : i64
    %1 = llvm.mlir.constant(1 : index) : i64
    %2 = llvm.mlir.constant(1 : i32) : i32
    %3 = llvm.mlir.constant(0 : i8) : i8
    %4 = llvm.alloca %1 x !llvm.struct<(i32, array<1024 x i8>)> : (i64) -> !llvm.ptr
    llvm.call @_ZN12KeyValuePairC1EiPKh(%4, %arg1, %arg2) : (!llvm.ptr, i32, !llvm.ptr) -> ()
    %5 = llvm.load %4 : !llvm.ptr -> !llvm.struct<(i32, array<1024 x i8>)>
    llvm.store %5, %arg0 : !llvm.struct<(i32, array<1024 x i8>)>, !llvm.ptr
    %6 = llvm.getelementptr %arg0[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    llvm.call @_ZN6MyListIP4NodeEC1Ev(%6) : (!llvm.ptr) -> ()
    %7 = llvm.getelementptr %arg0[0, 2] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    llvm.call @_ZNSt5mutexC1Ev(%7) : (!llvm.ptr) -> ()
    %8 = llvm.getelementptr %arg0[0, 3] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    llvm.store %3, %8 : i8, !llvm.ptr
    %9 = llvm.load %8 : !llvm.ptr -> i8
    llvm.store %9, %8 : i8, !llvm.ptr
    %10 = llvm.getelementptr %arg0[0, 4] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    llvm.store %3, %10 : i8, !llvm.ptr
    %11 = llvm.load %10 : !llvm.ptr -> i8
    llvm.store %11, %10 : i8, !llvm.ptr
    %12 = llvm.add %arg3, %2  : i32
    %13 = llvm.sext %12 : i32 to i64
    %14 = llvm.mlir.zero : !llvm.ptr
    llvm.br ^bb1(%0 : i64)
  ^bb1(%15: i64):  // 2 preds: ^bb0, ^bb2
    %16 = llvm.icmp "slt" %15, %13 : i64
    llvm.cond_br %16, ^bb2, ^bb3
  ^bb2:  // pred: ^bb1
    llvm.call @_ZN6MyListIP4NodeE4pushES1_(%6, %14) : (!llvm.ptr, !llvm.ptr) -> ()
    %17 = llvm.add %15, %1  : i64
    llvm.br ^bb1(%17 : i64)
  ^bb3:  // pred: ^bb1
    %18 = llvm.getelementptr %arg0[0, 5] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    llvm.store %arg3, %18 : i32, !llvm.ptr
    llvm.return
  }
  llvm.func @_ZN8SkipList4findEiPP4NodeS2_(%arg0: !llvm.ptr, %arg1: i32, %arg2: !llvm.ptr, %arg3: !llvm.ptr) -> i32 {
    %0 = llvm.mlir.constant(1 : i32) : i32
    %1 = llvm.mlir.constant(0 : index) : i64
    %2 = llvm.mlir.constant(1 : index) : i64
    %3 = llvm.mlir.constant(-1 : i32) : i32
    %4 = llvm.load %arg0 : !llvm.ptr -> !llvm.ptr
    %5 = llvm.mlir.addressof @_ZL9max_level : !llvm.ptr
    %6 = llvm.load %5 : !llvm.ptr -> i32
    %7 = llvm.add %6, %0  : i32
    %8 = llvm.sext %7 : i32 to i64
    %9 = llvm.sext %6 : i32 to i64
    llvm.br ^bb1(%1, %3, %4 : i64, i32, !llvm.ptr)
  ^bb1(%10: i64, %11: i32, %12: !llvm.ptr):  // 2 preds: ^bb0, ^bb9
    %13 = llvm.icmp "slt" %10, %8 : i64
    llvm.cond_br %13, ^bb2, ^bb10
  ^bb2:  // pred: ^bb1
    %14 = llvm.sub %9, %10  : i64
    %15 = llvm.trunc %14 : i64 to i32
    %16 = llvm.getelementptr %12[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %17 = llvm.sext %15 : i32 to i64
    %18 = llvm.call @_ZN6MyListIP4NodeEixEm(%16, %17) : (!llvm.ptr, i64) -> !llvm.ptr
    %19 = llvm.load %18 : !llvm.ptr -> !llvm.ptr
    llvm.br ^bb3(%19, %19, %12 : !llvm.ptr, !llvm.ptr, !llvm.ptr)
  ^bb3(%20: !llvm.ptr, %21: !llvm.ptr, %22: !llvm.ptr):  // 2 preds: ^bb2, ^bb4
    %23 = llvm.call @_ZN4Node7get_keyEv(%20) : (!llvm.ptr) -> i32
    %24 = llvm.icmp "sgt" %arg1, %23 : i32
    llvm.cond_br %24, ^bb4(%20 : !llvm.ptr), ^bb5
  ^bb4(%25: !llvm.ptr):  // pred: ^bb3
    %26 = llvm.getelementptr %25[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %27 = llvm.call @_ZN6MyListIP4NodeEixEm(%26, %17) : (!llvm.ptr, i64) -> !llvm.ptr
    %28 = llvm.load %27 : !llvm.ptr -> !llvm.ptr
    llvm.br ^bb3(%28, %28, %25 : !llvm.ptr, !llvm.ptr, !llvm.ptr)
  ^bb5:  // pred: ^bb3
    %29 = llvm.icmp "eq" %11, %3 : i32
    llvm.cond_br %29, ^bb6, ^bb7
  ^bb6:  // pred: ^bb5
    %30 = llvm.call @_ZN4Node7get_keyEv(%21) : (!llvm.ptr) -> i32
    %31 = llvm.icmp "eq" %arg1, %30 : i32
    %32 = llvm.select %31, %15, %11 : i1, i32
    llvm.br ^bb8(%32 : i32)
  ^bb7:  // pred: ^bb5
    llvm.br ^bb8(%11 : i32)
  ^bb8(%33: i32):  // 2 preds: ^bb6, ^bb7
    llvm.br ^bb9
  ^bb9:  // pred: ^bb8
    %34 = llvm.getelementptr %arg2[%14] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    llvm.store %22, %34 : !llvm.ptr, !llvm.ptr
    %35 = llvm.getelementptr %arg3[%14] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    llvm.store %21, %35 : !llvm.ptr, !llvm.ptr
    %36 = llvm.add %10, %2  : i64
    llvm.br ^bb1(%36, %33, %22 : i64, i32, !llvm.ptr)
  ^bb10:  // pred: ^bb1
    llvm.return %11 : i32
  }
  llvm.func linkonce_odr @_ZN6MyListIP4NodeEixEm(%arg0: !llvm.ptr, %arg1: i64) -> !llvm.ptr {
    %0 = llvm.mlir.constant(0 : index) : i64
    %1 = llvm.mlir.constant(1 : index) : i64
    %2 = llvm.load %arg0 : !llvm.ptr -> !llvm.ptr
    llvm.br ^bb1(%0, %2 : i64, !llvm.ptr)
  ^bb1(%3: i64, %4: !llvm.ptr):  // 2 preds: ^bb0, ^bb2
    %5 = llvm.icmp "slt" %3, %arg1 : i64
    llvm.cond_br %5, ^bb2, ^bb3
  ^bb2:  // pred: ^bb1
    %6 = llvm.getelementptr %4[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr)>
    %7 = llvm.load %6 : !llvm.ptr -> !llvm.ptr
    %8 = llvm.add %3, %1  : i64
    llvm.br ^bb1(%8, %7 : i64, !llvm.ptr)
  ^bb3:  // pred: ^bb1
    llvm.return %4 : !llvm.ptr
  }
  llvm.func linkonce_odr @_ZN4Node7get_keyEv(%arg0: !llvm.ptr) -> i32 {
    %0 = llvm.call @_ZNK12KeyValuePair7get_keyEv(%arg0) : (!llvm.ptr) -> i32
    llvm.return %0 : i32
  }
  llvm.func @_ZN8SkipList16get_random_levelEv(%arg0: !llvm.ptr) -> i32 {
    %0 = llvm.mlir.constant(1 : i32) : i32
    %1 = llvm.mlir.constant(5.000000e-01 : f64) : f64
    %2 = llvm.mlir.constant(2.14748365E+9 : f32) : f32
    %3 = llvm.mlir.constant(0 : i32) : i32
    %4 = llvm.mlir.constant(1 : index) : i64
    %5 = llvm.alloca %4 x i32 : (i64) -> !llvm.ptr
    llvm.store %3, %5 : i32, !llvm.ptr
    llvm.br ^bb1
  ^bb1:  // 2 preds: ^bb0, ^bb2
    %6 = llvm.call @rand() : () -> i32
    %7 = llvm.sitofp %6 : i32 to f32
    %8 = llvm.fdiv %7, %2  : f32
    %9 = llvm.fpext %8 : f32 to f64
    %10 = llvm.fcmp "ole" %9, %1 : f64
    llvm.cond_br %10, ^bb2, ^bb3
  ^bb2:  // pred: ^bb1
    %11 = llvm.load %5 : !llvm.ptr -> i32
    %12 = llvm.add %11, %0  : i32
    llvm.store %12, %5 : i32, !llvm.ptr
    llvm.br ^bb1
  ^bb3:  // pred: ^bb1
    %13 = llvm.load %5 : !llvm.ptr -> i32
    %14 = llvm.mlir.addressof @_ZL9max_level : !llvm.ptr
    %15 = llvm.load %14 : !llvm.ptr -> i32
    %16 = llvm.icmp "sgt" %13, %15 : i32
    llvm.cond_br %16, ^bb4(%14 : !llvm.ptr), ^bb4(%5 : !llvm.ptr)
  ^bb4(%17: !llvm.ptr):  // 2 preds: ^bb3, ^bb3
    llvm.br ^bb5(%17 : !llvm.ptr)
  ^bb5(%18: !llvm.ptr):  // pred: ^bb4
    llvm.br ^bb6
  ^bb6:  // pred: ^bb5
    %19 = llvm.load %18 : !llvm.ptr -> i32
    llvm.return %19 : i32
  }
  llvm.func @rand() -> i32 attributes {sym_visibility = "private"}
  llvm.func @_ZN8SkipList3addEiPKh(%arg0: !llvm.ptr, %arg1: i32, %arg2: !llvm.ptr) -> i8 {
    %0 = llvm.mlir.constant(1104 : i64) : i64
    %1 = llvm.mlir.constant(16 : i64) : i64
    %2 = llvm.mlir.constant(2.14748365E+9 : f32) : f32
    %3 = llvm.mlir.constant(5.000000e-01 : f64) : f64
    %4 = llvm.mlir.constant(0 : index) : i64
    %5 = llvm.mlir.constant(1 : index) : i64
    %6 = llvm.mlir.constant(true) : i1
    %7 = llvm.mlir.constant(0 : i32) : i32
    %8 = llvm.mlir.constant(1 : i8) : i8
    %9 = llvm.mlir.constant(false) : i1
    %10 = llvm.mlir.constant(0 : i8) : i8
    %11 = llvm.mlir.constant(-1 : i32) : i32
    %12 = llvm.mlir.constant(1 : i32) : i32
    %13 = llvm.mlir.undef : i8
    %14 = llvm.alloca %5 x i32 : (i64) -> !llvm.ptr
    llvm.store %7, %14 : i32, !llvm.ptr
    llvm.br ^bb1
  ^bb1:  // 2 preds: ^bb0, ^bb2
    %15 = llvm.call @rand() : () -> i32
    %16 = llvm.sitofp %15 : i32 to f32
    %17 = llvm.fdiv %16, %2  : f32
    %18 = llvm.fpext %17 : f32 to f64
    %19 = llvm.fcmp "ole" %18, %3 : f64
    llvm.cond_br %19, ^bb2, ^bb3
  ^bb2:  // pred: ^bb1
    %20 = llvm.load %14 : !llvm.ptr -> i32
    %21 = llvm.add %20, %12  : i32
    llvm.store %21, %14 : i32, !llvm.ptr
    llvm.br ^bb1
  ^bb3:  // pred: ^bb1
    %22 = llvm.load %14 : !llvm.ptr -> i32
    %23 = llvm.mlir.addressof @_ZL9max_level : !llvm.ptr
    %24 = llvm.load %23 : !llvm.ptr -> i32
    %25 = llvm.icmp "sgt" %22, %24 : i32
    llvm.cond_br %25, ^bb4(%23 : !llvm.ptr), ^bb4(%14 : !llvm.ptr)
  ^bb4(%26: !llvm.ptr):  // 2 preds: ^bb3, ^bb3
    llvm.br ^bb5(%26 : !llvm.ptr)
  ^bb5(%27: !llvm.ptr):  // pred: ^bb4
    llvm.br ^bb6
  ^bb6:  // pred: ^bb5
    %28 = llvm.load %27 : !llvm.ptr -> i32
    %29 = llvm.add %24, %12  : i32
    %30 = llvm.sext %29 : i32 to i64
    %31 = llvm.alloca %30 x !llvm.ptr : (i64) -> !llvm.ptr
    %32 = llvm.alloca %30 x !llvm.ptr : (i64) -> !llvm.ptr
    %33 = llvm.mlir.zero : !llvm.ptr
    llvm.br ^bb7(%4 : i64)
  ^bb7(%34: i64):  // 2 preds: ^bb6, ^bb8
    %35 = llvm.icmp "slt" %34, %30 : i64
    llvm.cond_br %35, ^bb8, ^bb9(%13, %6 : i8, i1)
  ^bb8:  // pred: ^bb7
    %36 = llvm.getelementptr %31[%34] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    llvm.store %33, %36 : !llvm.ptr, !llvm.ptr
    %37 = llvm.getelementptr %32[%34] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    llvm.store %33, %37 : !llvm.ptr, !llvm.ptr
    %38 = llvm.add %34, %5  : i64
    llvm.br ^bb7(%38 : i64)
  ^bb9(%39: i8, %40: i1):  // 2 preds: ^bb7, ^bb48
    llvm.br ^bb10(%39, %40 : i8, i1)
  ^bb10(%41: i8, %42: i1):  // pred: ^bb9
    llvm.cond_br %42, ^bb11(%41 : i8), ^bb49
  ^bb11(%43: i8):  // pred: ^bb10
    %44 = llvm.call @_ZN8SkipList4findEiPP4NodeS2_(%arg0, %arg1, %31, %32) : (!llvm.ptr, i32, !llvm.ptr, !llvm.ptr) -> i32
    %45 = llvm.icmp "ne" %44, %11 : i32
    %46 = llvm.icmp "eq" %44, %11 : i32
    llvm.cond_br %45, ^bb12, ^bb13
  ^bb12:  // pred: ^bb11
    %47 = llvm.sext %44 : i32 to i64
    %48 = llvm.getelementptr %32[%47] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    %49 = llvm.load %48 : !llvm.ptr -> !llvm.ptr
    %50 = llvm.getelementptr %49[0, 3] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %51 = llvm.load %50 : !llvm.ptr -> i8
    %52 = llvm.icmp "eq" %51, %10 : i8
    %53 = llvm.select %52, %10, %43 : i1, i8
    %54 = llvm.icmp "ne" %51, %10 : i8
    llvm.br ^bb14(%53, %54 : i8, i1)
  ^bb13:  // pred: ^bb11
    llvm.br ^bb14(%43, %6 : i8, i1)
  ^bb14(%55: i8, %56: i1):  // 2 preds: ^bb12, ^bb13
    llvm.br ^bb15
  ^bb15:  // pred: ^bb14
    llvm.cond_br %46, ^bb16, ^bb47(%55, %56 : i8, i1)
  ^bb16:  // pred: ^bb15
    llvm.br ^bb17(%7, %8, %33 : i32, i8, !llvm.ptr)
  ^bb17(%57: i32, %58: i8, %59: !llvm.ptr):  // 2 preds: ^bb16, ^bb29
    %60 = llvm.icmp "ne" %58, %10 : i8
    %61 = llvm.icmp "sle" %57, %28 : i32
    %62 = llvm.and %60, %61  : i1
    llvm.cond_br %62, ^bb18(%57, %59 : i32, !llvm.ptr), ^bb30
  ^bb18(%63: i32, %64: !llvm.ptr):  // pred: ^bb17
    %65 = llvm.sext %63 : i32 to i64
    %66 = llvm.getelementptr %31[%65] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    %67 = llvm.load %66 : !llvm.ptr -> !llvm.ptr
    %68 = llvm.getelementptr %32[%65] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    %69 = llvm.load %68 : !llvm.ptr -> !llvm.ptr
    %70 = llvm.call @_ZN13MakeshiftList8containsEP4Node(%64, %67) : (!llvm.ptr, !llvm.ptr) -> i8
    %71 = llvm.icmp "eq" %70, %10 : i8
    llvm.cond_br %71, ^bb19, ^bb20
  ^bb19:  // pred: ^bb18
    llvm.call @_ZN4Node4lockEv(%67) : (!llvm.ptr) -> ()
    %72 = llvm.udiv %1, %1  : i64
    %73 = llvm.mul %72, %1  : i64
    %74 = llvm.call @malloc(%73) : (i64) -> !llvm.ptr
    llvm.store %67, %74 : !llvm.ptr, !llvm.ptr
    %75 = llvm.getelementptr %74[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr)>
    llvm.store %64, %75 : !llvm.ptr, !llvm.ptr
    llvm.br ^bb21(%74 : !llvm.ptr)
  ^bb20:  // pred: ^bb18
    llvm.br ^bb21(%64 : !llvm.ptr)
  ^bb21(%76: !llvm.ptr):  // 2 preds: ^bb19, ^bb20
    llvm.br ^bb22
  ^bb22:  // pred: ^bb21
    %77 = llvm.getelementptr %67[0, 3] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %78 = llvm.load %77 : !llvm.ptr -> i8
    %79 = llvm.icmp "ne" %78, %10 : i8
    llvm.cond_br %79, ^bb23(%9 : i1), ^bb24
  ^bb23(%80: i1):  // 2 preds: ^bb22, ^bb27
    llvm.br ^bb28(%80 : i1)
  ^bb24:  // pred: ^bb22
    %81 = llvm.getelementptr %69[0, 3] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %82 = llvm.load %81 : !llvm.ptr -> i8
    %83 = llvm.icmp "ne" %82, %10 : i8
    llvm.cond_br %83, ^bb25, ^bb26
  ^bb25:  // pred: ^bb24
    llvm.br ^bb27(%9 : i1)
  ^bb26:  // pred: ^bb24
    %84 = llvm.getelementptr %67[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %85 = llvm.sext %63 : i32 to i64
    %86 = llvm.call @_ZN6MyListIP4NodeEixEm(%84, %85) : (!llvm.ptr, i64) -> !llvm.ptr
    %87 = llvm.load %86 : !llvm.ptr -> !llvm.ptr
    %88 = llvm.icmp "eq" %87, %69 : !llvm.ptr
    llvm.br ^bb27(%88 : i1)
  ^bb27(%89: i1):  // 2 preds: ^bb25, ^bb26
    llvm.br ^bb23(%89 : i1)
  ^bb28(%90: i1):  // pred: ^bb23
    llvm.br ^bb29
  ^bb29:  // pred: ^bb28
    %91 = llvm.zext %90 : i1 to i8
    %92 = llvm.add %63, %12  : i32
    llvm.br ^bb17(%92, %91, %76 : i32, i8, !llvm.ptr)
  ^bb30:  // pred: ^bb17
    %93 = llvm.icmp "eq" %58, %10 : i8
    %94 = llvm.icmp "ne" %58, %10 : i8
    llvm.cond_br %93, ^bb31, ^bb35
  ^bb31:  // pred: ^bb30
    llvm.br ^bb32(%59 : !llvm.ptr)
  ^bb32(%95: !llvm.ptr):  // 2 preds: ^bb31, ^bb33
    %96 = llvm.icmp "ne" %95, %33 : !llvm.ptr
    llvm.cond_br %96, ^bb33(%95 : !llvm.ptr), ^bb34
  ^bb33(%97: !llvm.ptr):  // pred: ^bb32
    %98 = llvm.load %97 : !llvm.ptr -> !llvm.ptr
    llvm.call @_ZN4Node6unlockEv(%98) : (!llvm.ptr) -> ()
    %99 = llvm.getelementptr %97[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr)>
    %100 = llvm.load %99 : !llvm.ptr -> !llvm.ptr
    llvm.br ^bb32(%100 : !llvm.ptr)
  ^bb34:  // pred: ^bb32
    llvm.br ^bb35
  ^bb35:  // 2 preds: ^bb30, ^bb34
    %101 = llvm.select %94, %8, %55 : i1, i8
    %102 = llvm.and %93, %56  : i1
    llvm.cond_br %94, ^bb36, ^bb45
  ^bb36:  // pred: ^bb35
    %103 = llvm.mul %0, %5  : i64
    %104 = llvm.call @malloc(%103) : (i64) -> !llvm.ptr
    llvm.call @_ZN4NodeC1EiPKhi(%104, %arg1, %arg2, %28) : (!llvm.ptr, i32, !llvm.ptr, i32) -> ()
    %105 = llvm.add %28, %12  : i32
    %106 = llvm.sext %105 : i32 to i64
    %107 = llvm.getelementptr %104[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    llvm.br ^bb37(%4 : i64)
  ^bb37(%108: i64):  // 2 preds: ^bb36, ^bb38
    %109 = llvm.icmp "slt" %108, %106 : i64
    llvm.cond_br %109, ^bb38, ^bb39
  ^bb38:  // pred: ^bb37
    %110 = llvm.trunc %108 : i64 to i32
    %111 = llvm.sext %110 : i32 to i64
    %112 = llvm.call @_ZN6MyListIP4NodeEixEm(%107, %111) : (!llvm.ptr, i64) -> !llvm.ptr
    %113 = llvm.getelementptr %32[%108] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    %114 = llvm.load %113 : !llvm.ptr -> !llvm.ptr
    llvm.store %114, %112 : !llvm.ptr, !llvm.ptr
    %115 = llvm.add %108, %5  : i64
    llvm.br ^bb37(%115 : i64)
  ^bb39:  // pred: ^bb37
    llvm.br ^bb40(%4 : i64)
  ^bb40(%116: i64):  // 2 preds: ^bb39, ^bb41
    %117 = llvm.icmp "slt" %116, %106 : i64
    llvm.cond_br %117, ^bb41, ^bb42
  ^bb41:  // pred: ^bb40
    %118 = llvm.trunc %116 : i64 to i32
    %119 = llvm.getelementptr %31[%116] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    %120 = llvm.load %119 : !llvm.ptr -> !llvm.ptr
    %121 = llvm.getelementptr %120[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %122 = llvm.sext %118 : i32 to i64
    %123 = llvm.call @_ZN6MyListIP4NodeEixEm(%121, %122) : (!llvm.ptr, i64) -> !llvm.ptr
    llvm.store %104, %123 : !llvm.ptr, !llvm.ptr
    %124 = llvm.add %116, %5  : i64
    llvm.br ^bb40(%124 : i64)
  ^bb42:  // pred: ^bb40
    %125 = llvm.getelementptr %104[0, 4] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    llvm.store %8, %125 : i8, !llvm.ptr
    llvm.br ^bb43(%59 : !llvm.ptr)
  ^bb43(%126: !llvm.ptr):  // 2 preds: ^bb42, ^bb44
    %127 = llvm.icmp "ne" %126, %33 : !llvm.ptr
    llvm.cond_br %127, ^bb44(%126 : !llvm.ptr), ^bb45
  ^bb44(%128: !llvm.ptr):  // pred: ^bb43
    %129 = llvm.load %128 : !llvm.ptr -> !llvm.ptr
    llvm.call @_ZN4Node6unlockEv(%129) : (!llvm.ptr) -> ()
    %130 = llvm.getelementptr %128[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr)>
    %131 = llvm.load %130 : !llvm.ptr -> !llvm.ptr
    llvm.br ^bb43(%131 : !llvm.ptr)
  ^bb45:  // 2 preds: ^bb35, ^bb43
    llvm.br ^bb46
  ^bb46:  // pred: ^bb45
    llvm.br ^bb47(%101, %102 : i8, i1)
  ^bb47(%132: i8, %133: i1):  // 2 preds: ^bb15, ^bb46
    llvm.br ^bb48(%132, %133 : i8, i1)
  ^bb48(%134: i8, %135: i1):  // pred: ^bb47
    llvm.br ^bb9(%134, %135 : i8, i1)
  ^bb49:  // pred: ^bb10
    llvm.return %41 : i8
  }
  llvm.func linkonce_odr @_ZN13MakeshiftList8containsEP4Node(%arg0: !llvm.ptr, %arg1: !llvm.ptr) -> i8 {
    %0 = llvm.mlir.constant(true) : i1
    %1 = llvm.mlir.constant(0 : i8) : i8
    %2 = llvm.mlir.constant(1 : i8) : i8
    %3 = llvm.mlir.undef : i8
    %4 = llvm.mlir.zero : !llvm.ptr
    llvm.br ^bb1(%0, %3, %0, %arg0 : i1, i8, i1, !llvm.ptr)
  ^bb1(%5: i1, %6: i8, %7: i1, %8: !llvm.ptr):  // 2 preds: ^bb0, ^bb6
    %9 = llvm.icmp "ne" %8, %4 : !llvm.ptr
    %10 = llvm.and %9, %7  : i1
    llvm.cond_br %10, ^bb2(%5, %6, %8 : i1, i8, !llvm.ptr), ^bb7
  ^bb2(%11: i1, %12: i8, %13: !llvm.ptr):  // pred: ^bb1
    %14 = llvm.load %13 : !llvm.ptr -> !llvm.ptr
    %15 = llvm.icmp "eq" %14, %arg1 : !llvm.ptr
    %16 = llvm.xor %15, %0  : i1
    %17 = llvm.and %16, %11  : i1
    %18 = llvm.select %15, %2, %12 : i1, i8
    llvm.cond_br %15, ^bb3, ^bb4
  ^bb3:  // pred: ^bb2
    llvm.br ^bb5(%13 : !llvm.ptr)
  ^bb4:  // pred: ^bb2
    %19 = llvm.getelementptr %13[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr)>
    %20 = llvm.load %19 : !llvm.ptr -> !llvm.ptr
    llvm.br ^bb5(%20 : !llvm.ptr)
  ^bb5(%21: !llvm.ptr):  // 2 preds: ^bb3, ^bb4
    llvm.br ^bb6
  ^bb6:  // pred: ^bb5
    llvm.br ^bb1(%17, %18, %16, %21 : i1, i8, i1, !llvm.ptr)
  ^bb7:  // pred: ^bb1
    %22 = llvm.select %5, %1, %6 : i1, i8
    llvm.return %22 : i8
  }
  llvm.func linkonce_odr @_ZN4Node4lockEv(%arg0: !llvm.ptr) {
    %0 = llvm.getelementptr %arg0[0, 2] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    llvm.call @_ZNSt5mutex4lockEv(%0) : (!llvm.ptr) -> ()
    llvm.return
  }
  llvm.func linkonce_odr @_ZN4Node6unlockEv(%arg0: !llvm.ptr) {
    %0 = llvm.getelementptr %arg0[0, 2] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    llvm.call @_ZNSt5mutex6unlockEv(%0) : (!llvm.ptr) -> ()
    llvm.return
  }
  llvm.func @_ZN8SkipList6searchEi(%arg0: !llvm.ptr, %arg1: i32) -> !llvm.ptr {
    %0 = llvm.mlir.constant(0 : index) : i64
    %1 = llvm.mlir.constant(1 : index) : i64
    %2 = llvm.mlir.constant(false) : i1
    %3 = llvm.mlir.constant(0 : i8) : i8
    %4 = llvm.mlir.constant(-1 : i32) : i32
    %5 = llvm.mlir.constant(0 : i64) : i64
    %6 = llvm.mlir.constant(1 : i32) : i32
    %7 = llvm.alloca %1 x !llvm.ptr : (i64) -> !llvm.ptr
    %8 = llvm.mlir.addressof @_ZL9max_level : !llvm.ptr
    %9 = llvm.load %8 : !llvm.ptr -> i32
    %10 = llvm.add %9, %6  : i32
    %11 = llvm.sext %10 : i32 to i64
    %12 = llvm.alloca %11 x !llvm.ptr : (i64) -> !llvm.ptr
    %13 = llvm.alloca %11 x !llvm.ptr : (i64) -> !llvm.ptr
    %14 = llvm.mlir.zero : !llvm.ptr
    llvm.br ^bb1(%0 : i64)
  ^bb1(%15: i64):  // 2 preds: ^bb0, ^bb2
    %16 = llvm.icmp "slt" %15, %11 : i64
    llvm.cond_br %16, ^bb2, ^bb3
  ^bb2:  // pred: ^bb1
    %17 = llvm.getelementptr %12[%15] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    llvm.store %14, %17 : !llvm.ptr, !llvm.ptr
    %18 = llvm.getelementptr %13[%15] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    llvm.store %14, %18 : !llvm.ptr, !llvm.ptr
    %19 = llvm.add %15, %1  : i64
    llvm.br ^bb1(%19 : i64)
  ^bb3:  // pred: ^bb1
    %20 = llvm.call @_ZN8SkipList4findEiPP4NodeS2_(%arg0, %arg1, %12, %13) : (!llvm.ptr, i32, !llvm.ptr, !llvm.ptr) -> i32
    %21 = llvm.sext %20 : i32 to i64
    %22 = llvm.icmp "ne" %20, %4 : i32
    %23 = llvm.icmp "eq" %20, %4 : i32
    llvm.cond_br %23, ^bb4, ^bb5
  ^bb4:  // pred: ^bb3
    %24 = llvm.getelementptr %7[] : (!llvm.ptr) -> !llvm.ptr, !llvm.ptr
    llvm.store %14, %24 : !llvm.ptr, !llvm.ptr
    llvm.br ^bb5
  ^bb5:  // 2 preds: ^bb3, ^bb4
    llvm.cond_br %22, ^bb6, ^bb32
  ^bb6:  // pred: ^bb5
    %25 = llvm.load %arg0 : !llvm.ptr -> !llvm.ptr
    %26 = llvm.load %8 : !llvm.ptr -> i32
    %27 = llvm.add %26, %6  : i32
    %28 = llvm.sext %27 : i32 to i64
    %29 = llvm.sext %26 : i32 to i64
    llvm.br ^bb7(%0, %25 : i64, !llvm.ptr)
  ^bb7(%30: i64, %31: !llvm.ptr):  // 2 preds: ^bb6, ^bb17
    %32 = llvm.icmp "slt" %30, %28 : i64
    llvm.cond_br %32, ^bb8, ^bb18
  ^bb8:  // pred: ^bb7
    %33 = llvm.sub %29, %30  : i64
    %34 = llvm.trunc %33 : i64 to i32
    %35 = llvm.sext %34 : i32 to i64
    llvm.br ^bb9(%31 : !llvm.ptr)
  ^bb9(%36: !llvm.ptr):  // 2 preds: ^bb8, ^bb16
    %37 = llvm.getelementptr %36[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %38 = llvm.call @_ZN6MyListIP4NodeEixEm(%37, %35) : (!llvm.ptr, i64) -> !llvm.ptr
    %39 = llvm.load %38 : !llvm.ptr -> !llvm.ptr
    %40 = llvm.icmp "ne" %39, %14 : !llvm.ptr
    llvm.cond_br %40, ^bb10, ^bb14(%2, %36 : i1, !llvm.ptr)
  ^bb10:  // pred: ^bb9
    %41 = llvm.call @_ZN6MyListIP4NodeEixEm(%37, %35) : (!llvm.ptr, i64) -> !llvm.ptr
    %42 = llvm.load %41 : !llvm.ptr -> !llvm.ptr
    %43 = llvm.call @_ZN4Node7get_keyEv(%42) : (!llvm.ptr) -> i32
    %44 = llvm.icmp "sgt" %arg1, %43 : i32
    llvm.cond_br %44, ^bb11, ^bb12
  ^bb11:  // pred: ^bb10
    %45 = llvm.call @_ZN6MyListIP4NodeEixEm(%37, %35) : (!llvm.ptr, i64) -> !llvm.ptr
    %46 = llvm.load %45 : !llvm.ptr -> !llvm.ptr
    llvm.br ^bb13(%46 : !llvm.ptr)
  ^bb12:  // pred: ^bb10
    llvm.br ^bb13(%36 : !llvm.ptr)
  ^bb13(%47: !llvm.ptr):  // 2 preds: ^bb11, ^bb12
    llvm.br ^bb14(%44, %47 : i1, !llvm.ptr)
  ^bb14(%48: i1, %49: !llvm.ptr):  // 2 preds: ^bb9, ^bb13
    llvm.br ^bb15(%48, %49 : i1, !llvm.ptr)
  ^bb15(%50: i1, %51: !llvm.ptr):  // pred: ^bb14
    llvm.br ^bb16
  ^bb16:  // pred: ^bb15
    llvm.cond_br %50, ^bb9(%51 : !llvm.ptr), ^bb17
  ^bb17:  // pred: ^bb16
    %52 = llvm.add %30, %1  : i64
    llvm.br ^bb7(%52, %51 : i64, !llvm.ptr)
  ^bb18:  // pred: ^bb7
    %53 = llvm.getelementptr %31[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %54 = llvm.call @_ZN6MyListIP4NodeEixEm(%53, %5) : (!llvm.ptr, i64) -> !llvm.ptr
    %55 = llvm.load %54 : !llvm.ptr -> !llvm.ptr
    %56 = llvm.icmp "ne" %55, %14 : !llvm.ptr
    llvm.cond_br %56, ^bb19, ^bb26(%2 : i1)
  ^bb19:  // pred: ^bb18
    %57 = llvm.call @_ZN4Node7get_keyEv(%55) : (!llvm.ptr) -> i32
    %58 = llvm.icmp "eq" %57, %arg1 : i32
    llvm.cond_br %58, ^bb20, ^bb24(%2 : i1)
  ^bb20:  // pred: ^bb19
    %59 = llvm.getelementptr %13[%21] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    %60 = llvm.load %59 : !llvm.ptr -> !llvm.ptr
    %61 = llvm.getelementptr %60[0, 4] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %62 = llvm.load %61 : !llvm.ptr -> i8
    %63 = llvm.icmp "ne" %62, %3 : i8
    llvm.cond_br %63, ^bb21, ^bb22
  ^bb21:  // pred: ^bb20
    %64 = llvm.getelementptr %60[0, 3] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %65 = llvm.load %64 : !llvm.ptr -> i8
    %66 = llvm.icmp "eq" %65, %3 : i8
    llvm.br ^bb23(%66 : i1)
  ^bb22:  // pred: ^bb20
    llvm.br ^bb23(%2 : i1)
  ^bb23(%67: i1):  // 2 preds: ^bb21, ^bb22
    llvm.br ^bb24(%67 : i1)
  ^bb24(%68: i1):  // 2 preds: ^bb19, ^bb23
    llvm.br ^bb25(%68 : i1)
  ^bb25(%69: i1):  // pred: ^bb24
    llvm.br ^bb26(%69 : i1)
  ^bb26(%70: i1):  // 2 preds: ^bb18, ^bb25
    llvm.br ^bb27(%70 : i1)
  ^bb27(%71: i1):  // pred: ^bb26
    llvm.br ^bb28
  ^bb28:  // pred: ^bb27
    llvm.cond_br %71, ^bb29, ^bb30
  ^bb29:  // pred: ^bb28
    %72 = llvm.call @_ZN4Node9get_valueEv(%55) : (!llvm.ptr) -> !llvm.ptr
    %73 = llvm.getelementptr %7[] : (!llvm.ptr) -> !llvm.ptr, !llvm.ptr
    llvm.store %72, %73 : !llvm.ptr, !llvm.ptr
    llvm.br ^bb31
  ^bb30:  // pred: ^bb28
    %74 = llvm.getelementptr %7[] : (!llvm.ptr) -> !llvm.ptr, !llvm.ptr
    llvm.store %14, %74 : !llvm.ptr, !llvm.ptr
    llvm.br ^bb31
  ^bb31:  // 2 preds: ^bb29, ^bb30
    llvm.br ^bb32
  ^bb32:  // 2 preds: ^bb5, ^bb31
    %75 = llvm.getelementptr %7[] : (!llvm.ptr) -> !llvm.ptr, !llvm.ptr
    %76 = llvm.load %75 : !llvm.ptr -> !llvm.ptr
    llvm.return %76 : !llvm.ptr
  }
  llvm.func linkonce_odr @_ZN4Node9get_valueEv(%arg0: !llvm.ptr) -> !llvm.ptr {
    %0 = llvm.call @_ZN12KeyValuePair9get_valueEv(%arg0) : (!llvm.ptr) -> !llvm.ptr
    llvm.return %0 : !llvm.ptr
  }
  llvm.func @_ZN8SkipList6removeEi(%arg0: !llvm.ptr, %arg1: i32) -> i8 {
    %0 = llvm.mlir.constant(16 : i64) : i64
    %1 = llvm.mlir.constant(0 : index) : i64
    %2 = llvm.mlir.constant(1 : index) : i64
    %3 = llvm.mlir.constant(true) : i1
    %4 = llvm.mlir.constant(0 : i32) : i32
    %5 = llvm.mlir.constant(1 : i8) : i8
    %6 = llvm.mlir.constant(false) : i1
    %7 = llvm.mlir.constant(1 : i32) : i32
    %8 = llvm.mlir.constant(-1 : i32) : i32
    %9 = llvm.mlir.constant(0 : i8) : i8
    %10 = llvm.mlir.undef : i8
    %11 = llvm.mlir.zero : !llvm.ptr
    %12 = llvm.mlir.addressof @_ZL9max_level : !llvm.ptr
    %13 = llvm.load %12 : !llvm.ptr -> i32
    %14 = llvm.add %13, %7  : i32
    %15 = llvm.sext %14 : i32 to i64
    %16 = llvm.alloca %15 x !llvm.ptr : (i64) -> !llvm.ptr
    %17 = llvm.alloca %15 x !llvm.ptr : (i64) -> !llvm.ptr
    llvm.br ^bb1(%1 : i64)
  ^bb1(%18: i64):  // 2 preds: ^bb0, ^bb2
    %19 = llvm.icmp "slt" %18, %15 : i64
    llvm.cond_br %19, ^bb2, ^bb3(%8, %9, %10, %3, %11 : i32, i8, i8, i1, !llvm.ptr)
  ^bb2:  // pred: ^bb1
    %20 = llvm.getelementptr %16[%18] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    llvm.store %11, %20 : !llvm.ptr, !llvm.ptr
    %21 = llvm.getelementptr %17[%18] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    llvm.store %11, %21 : !llvm.ptr, !llvm.ptr
    %22 = llvm.add %18, %2  : i64
    llvm.br ^bb1(%22 : i64)
  ^bb3(%23: i32, %24: i8, %25: i8, %26: i1, %27: !llvm.ptr):  // 2 preds: ^bb1, ^bb59
    llvm.br ^bb4(%23, %24, %25, %26, %27 : i32, i8, i8, i1, !llvm.ptr)
  ^bb4(%28: i32, %29: i8, %30: i8, %31: i1, %32: !llvm.ptr):  // pred: ^bb3
    llvm.cond_br %31, ^bb5(%30, %28, %29, %32 : i8, i32, i8, !llvm.ptr), ^bb60
  ^bb5(%33: i8, %34: i32, %35: i8, %36: !llvm.ptr):  // pred: ^bb4
    %37 = llvm.call @_ZN8SkipList4findEiPP4NodeS2_(%arg0, %arg1, %16, %17) : (!llvm.ptr, i32, !llvm.ptr, !llvm.ptr) -> i32
    %38 = llvm.icmp "ne" %37, %8 : i32
    llvm.cond_br %38, ^bb6, ^bb7
  ^bb6:  // pred: ^bb5
    %39 = llvm.sext %37 : i32 to i64
    %40 = llvm.getelementptr %17[%39] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    %41 = llvm.load %40 : !llvm.ptr -> !llvm.ptr
    llvm.br ^bb8(%41 : !llvm.ptr)
  ^bb7:  // pred: ^bb5
    llvm.br ^bb8(%36 : !llvm.ptr)
  ^bb8(%42: !llvm.ptr):  // 2 preds: ^bb6, ^bb7
    llvm.br ^bb9
  ^bb9:  // pred: ^bb8
    %43 = llvm.icmp "ne" %35, %9 : i8
    llvm.cond_br %43, ^bb10(%3 : i1), ^bb11
  ^bb10(%44: i1):  // 2 preds: ^bb9, ^bb20
    llvm.br ^bb21(%44 : i1)
  ^bb11:  // pred: ^bb9
    llvm.cond_br %38, ^bb12, ^bb19(%6 : i1)
  ^bb12:  // pred: ^bb11
    %45 = llvm.getelementptr %42[0, 4] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %46 = llvm.load %45 : !llvm.ptr -> i8
    %47 = llvm.icmp "ne" %46, %9 : i8
    llvm.cond_br %47, ^bb13, ^bb17(%6 : i1)
  ^bb13:  // pred: ^bb12
    %48 = llvm.getelementptr %42[0, 5] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %49 = llvm.load %48 : !llvm.ptr -> i32
    %50 = llvm.icmp "eq" %49, %37 : i32
    llvm.cond_br %50, ^bb14, ^bb15
  ^bb14:  // pred: ^bb13
    %51 = llvm.getelementptr %42[0, 3] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %52 = llvm.load %51 : !llvm.ptr -> i8
    %53 = llvm.icmp "eq" %52, %9 : i8
    llvm.br ^bb16(%53 : i1)
  ^bb15:  // pred: ^bb13
    llvm.br ^bb16(%6 : i1)
  ^bb16(%54: i1):  // 2 preds: ^bb14, ^bb15
    llvm.br ^bb17(%54 : i1)
  ^bb17(%55: i1):  // 2 preds: ^bb12, ^bb16
    llvm.br ^bb18(%55 : i1)
  ^bb18(%56: i1):  // pred: ^bb17
    llvm.br ^bb19(%56 : i1)
  ^bb19(%57: i1):  // 2 preds: ^bb11, ^bb18
    llvm.br ^bb20(%57 : i1)
  ^bb20(%58: i1):  // pred: ^bb19
    llvm.br ^bb10(%58 : i1)
  ^bb21(%59: i1):  // pred: ^bb10
    llvm.br ^bb22
  ^bb22:  // pred: ^bb21
    llvm.cond_br %59, ^bb23, ^bb58(%34, %35, %9, %6 : i32, i8, i8, i1)
  ^bb23:  // pred: ^bb22
    %60 = llvm.icmp "eq" %35, %9 : i8
    llvm.cond_br %60, ^bb24, ^bb28(%34, %35, %33, %3 : i32, i8, i8, i1)
  ^bb24:  // pred: ^bb23
    %61 = llvm.getelementptr %42[0, 5] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %62 = llvm.load %61 : !llvm.ptr -> i32
    llvm.call @_ZN4Node4lockEv(%42) : (!llvm.ptr) -> ()
    %63 = llvm.getelementptr %42[0, 3] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %64 = llvm.load %63 : !llvm.ptr -> i8
    %65 = llvm.icmp "ne" %64, %9 : i8
    %66 = llvm.select %65, %9, %33 : i1, i8
    %67 = llvm.icmp "eq" %64, %9 : i8
    llvm.cond_br %65, ^bb25, ^bb26
  ^bb25:  // pred: ^bb24
    llvm.call @_ZN4Node6unlockEv(%42) : (!llvm.ptr) -> ()
    llvm.br ^bb26
  ^bb26:  // 2 preds: ^bb24, ^bb25
    %68 = llvm.select %67, %5, %35 : i1, i8
    llvm.cond_br %67, ^bb27, ^bb28(%62, %68, %66, %67 : i32, i8, i8, i1)
  ^bb27:  // pred: ^bb26
    llvm.store %5, %63 : i8, !llvm.ptr
    llvm.br ^bb28(%62, %68, %66, %67 : i32, i8, i8, i1)
  ^bb28(%69: i32, %70: i8, %71: i8, %72: i1):  // 3 preds: ^bb23, ^bb26, ^bb27
    llvm.br ^bb29(%69, %70, %71, %72 : i32, i8, i8, i1)
  ^bb29(%73: i32, %74: i8, %75: i8, %76: i1):  // pred: ^bb28
    llvm.br ^bb30
  ^bb30:  // pred: ^bb29
    llvm.cond_br %76, ^bb31, ^bb56(%75, %6 : i8, i1)
  ^bb31:  // pred: ^bb30
    llvm.br ^bb32(%4, %5, %11 : i32, i8, !llvm.ptr)
  ^bb32(%77: i32, %78: i8, %79: !llvm.ptr):  // 2 preds: ^bb31, ^bb41
    %80 = llvm.icmp "ne" %78, %9 : i8
    %81 = llvm.icmp "sle" %77, %73 : i32
    %82 = llvm.and %80, %81  : i1
    llvm.cond_br %82, ^bb33(%77, %79 : i32, !llvm.ptr), ^bb42
  ^bb33(%83: i32, %84: !llvm.ptr):  // pred: ^bb32
    %85 = llvm.sext %83 : i32 to i64
    %86 = llvm.getelementptr %16[%85] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    %87 = llvm.load %86 : !llvm.ptr -> !llvm.ptr
    %88 = llvm.call @_ZN13MakeshiftList8containsEP4Node(%84, %87) : (!llvm.ptr, !llvm.ptr) -> i8
    %89 = llvm.icmp "eq" %88, %9 : i8
    llvm.cond_br %89, ^bb34, ^bb35
  ^bb34:  // pred: ^bb33
    llvm.call @_ZN4Node4lockEv(%87) : (!llvm.ptr) -> ()
    %90 = llvm.udiv %0, %0  : i64
    %91 = llvm.mul %90, %0  : i64
    %92 = llvm.call @malloc(%91) : (i64) -> !llvm.ptr
    llvm.store %87, %92 : !llvm.ptr, !llvm.ptr
    %93 = llvm.getelementptr %92[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr)>
    llvm.store %84, %93 : !llvm.ptr, !llvm.ptr
    llvm.br ^bb36(%92 : !llvm.ptr)
  ^bb35:  // pred: ^bb33
    llvm.br ^bb36(%84 : !llvm.ptr)
  ^bb36(%94: !llvm.ptr):  // 2 preds: ^bb34, ^bb35
    llvm.br ^bb37
  ^bb37:  // pred: ^bb36
    %95 = llvm.getelementptr %87[0, 3] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %96 = llvm.load %95 : !llvm.ptr -> i8
    %97 = llvm.icmp "ne" %96, %9 : i8
    llvm.cond_br %97, ^bb38, ^bb39
  ^bb38:  // pred: ^bb37
    llvm.br ^bb40(%6 : i1)
  ^bb39:  // pred: ^bb37
    %98 = llvm.getelementptr %87[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %99 = llvm.sext %83 : i32 to i64
    %100 = llvm.call @_ZN6MyListIP4NodeEixEm(%98, %99) : (!llvm.ptr, i64) -> !llvm.ptr
    %101 = llvm.load %100 : !llvm.ptr -> !llvm.ptr
    %102 = llvm.icmp "eq" %101, %42 : !llvm.ptr
    llvm.br ^bb40(%102 : i1)
  ^bb40(%103: i1):  // 2 preds: ^bb38, ^bb39
    llvm.br ^bb41
  ^bb41:  // pred: ^bb40
    %104 = llvm.zext %103 : i1 to i8
    %105 = llvm.add %83, %7  : i32
    llvm.br ^bb32(%105, %104, %94 : i32, i8, !llvm.ptr)
  ^bb42:  // pred: ^bb32
    %106 = llvm.icmp "eq" %78, %9 : i8
    %107 = llvm.icmp "ne" %78, %9 : i8
    llvm.cond_br %106, ^bb43, ^bb47
  ^bb43:  // pred: ^bb42
    llvm.br ^bb44(%79 : !llvm.ptr)
  ^bb44(%108: !llvm.ptr):  // 2 preds: ^bb43, ^bb45
    %109 = llvm.icmp "ne" %108, %11 : !llvm.ptr
    llvm.cond_br %109, ^bb45(%108 : !llvm.ptr), ^bb46
  ^bb45(%110: !llvm.ptr):  // pred: ^bb44
    %111 = llvm.load %110 : !llvm.ptr -> !llvm.ptr
    llvm.call @_ZN4Node6unlockEv(%111) : (!llvm.ptr) -> ()
    %112 = llvm.getelementptr %110[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr)>
    %113 = llvm.load %112 : !llvm.ptr -> !llvm.ptr
    llvm.br ^bb44(%113 : !llvm.ptr)
  ^bb46:  // pred: ^bb44
    llvm.br ^bb47
  ^bb47:  // 2 preds: ^bb42, ^bb46
    %114 = llvm.select %107, %5, %75 : i1, i8
    llvm.cond_br %107, ^bb48, ^bb54
  ^bb48:  // pred: ^bb47
    %115 = llvm.add %73, %7  : i32
    %116 = llvm.sext %115 : i32 to i64
    %117 = llvm.sext %73 : i32 to i64
    %118 = llvm.getelementptr %42[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    llvm.br ^bb49(%1 : i64)
  ^bb49(%119: i64):  // 2 preds: ^bb48, ^bb50
    %120 = llvm.icmp "slt" %119, %116 : i64
    llvm.cond_br %120, ^bb50, ^bb51
  ^bb50:  // pred: ^bb49
    %121 = llvm.sub %117, %119  : i64
    %122 = llvm.trunc %121 : i64 to i32
    %123 = llvm.getelementptr %16[%121] : (!llvm.ptr, i64) -> !llvm.ptr, !llvm.ptr
    %124 = llvm.load %123 : !llvm.ptr -> !llvm.ptr
    %125 = llvm.getelementptr %124[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %126 = llvm.sext %122 : i32 to i64
    %127 = llvm.call @_ZN6MyListIP4NodeEixEm(%125, %126) : (!llvm.ptr, i64) -> !llvm.ptr
    %128 = llvm.call @_ZN6MyListIP4NodeEixEm(%118, %126) : (!llvm.ptr, i64) -> !llvm.ptr
    %129 = llvm.load %128 : !llvm.ptr -> !llvm.ptr
    llvm.store %129, %127 : !llvm.ptr, !llvm.ptr
    %130 = llvm.add %119, %2  : i64
    llvm.br ^bb49(%130 : i64)
  ^bb51:  // pred: ^bb49
    llvm.call @_ZN4Node6unlockEv(%42) : (!llvm.ptr) -> ()
    llvm.br ^bb52(%79 : !llvm.ptr)
  ^bb52(%131: !llvm.ptr):  // 2 preds: ^bb51, ^bb53
    %132 = llvm.icmp "ne" %131, %11 : !llvm.ptr
    llvm.cond_br %132, ^bb53(%131 : !llvm.ptr), ^bb54
  ^bb53(%133: !llvm.ptr):  // pred: ^bb52
    %134 = llvm.load %133 : !llvm.ptr -> !llvm.ptr
    llvm.call @_ZN4Node6unlockEv(%134) : (!llvm.ptr) -> ()
    %135 = llvm.getelementptr %133[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr)>
    %136 = llvm.load %135 : !llvm.ptr -> !llvm.ptr
    llvm.br ^bb52(%136 : !llvm.ptr)
  ^bb54:  // 2 preds: ^bb47, ^bb52
    llvm.br ^bb55
  ^bb55:  // pred: ^bb54
    llvm.br ^bb56(%114, %106 : i8, i1)
  ^bb56(%137: i8, %138: i1):  // 2 preds: ^bb30, ^bb55
    llvm.br ^bb57(%137, %138 : i8, i1)
  ^bb57(%139: i8, %140: i1):  // pred: ^bb56
    llvm.br ^bb58(%73, %74, %139, %140 : i32, i8, i8, i1)
  ^bb58(%141: i32, %142: i8, %143: i8, %144: i1):  // 2 preds: ^bb22, ^bb57
    llvm.br ^bb59(%141, %142, %143, %144 : i32, i8, i8, i1)
  ^bb59(%145: i32, %146: i8, %147: i8, %148: i1):  // pred: ^bb58
    llvm.br ^bb3(%145, %146, %147, %148, %42 : i32, i8, i8, i1, !llvm.ptr)
  ^bb60:  // pred: ^bb4
    llvm.return %30 : i8
  }
  llvm.func @_ZN8SkipList7displayEv(%arg0: !llvm.ptr) {
    %0 = llvm.mlir.constant(true) : i1
    %1 = llvm.mlir.constant(3 : i32) : i32
    %2 = llvm.mlir.constant(1 : i32) : i32
    %3 = llvm.mlir.constant(false) : i1
    %4 = llvm.mlir.constant(2147483647 : i32) : i32
    %5 = llvm.mlir.constant(-2147483648 : i32) : i32
    %6 = llvm.mlir.constant(0 : i32) : i32
    %7 = llvm.mlir.addressof @_ZL9max_level : !llvm.ptr
    llvm.br ^bb1(%6, %0 : i32, i1)
  ^bb1(%8: i32, %9: i1):  // 2 preds: ^bb0, ^bb17
    %10 = llvm.load %7 : !llvm.ptr -> i32
    %11 = llvm.icmp "sle" %8, %10 : i32
    %12 = llvm.and %11, %9  : i1
    llvm.cond_br %12, ^bb2(%8 : i32), ^bb18
  ^bb2(%13: i32):  // pred: ^bb1
    %14 = llvm.load %arg0 : !llvm.ptr -> !llvm.ptr
    %15 = llvm.call @_ZN4Node7get_keyEv(%14) : (!llvm.ptr) -> i32
    %16 = llvm.icmp "eq" %15, %5 : i32
    llvm.cond_br %16, ^bb3, ^bb4
  ^bb3:  // pred: ^bb2
    %17 = llvm.getelementptr %14[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %18 = llvm.sext %13 : i32 to i64
    %19 = llvm.call @_ZN6MyListIP4NodeEixEm(%17, %18) : (!llvm.ptr, i64) -> !llvm.ptr
    %20 = llvm.load %19 : !llvm.ptr -> !llvm.ptr
    %21 = llvm.call @_ZN4Node7get_keyEv(%20) : (!llvm.ptr) -> i32
    %22 = llvm.icmp "eq" %21, %4 : i32
    llvm.br ^bb5(%22 : i1)
  ^bb4:  // pred: ^bb2
    llvm.br ^bb5(%3 : i1)
  ^bb5(%23: i1):  // 2 preds: ^bb3, ^bb4
    llvm.br ^bb6
  ^bb6:  // pred: ^bb5
    llvm.cond_br %23, ^bb7, ^bb8
  ^bb7:  // pred: ^bb6
    llvm.br ^bb12(%0 : i1)
  ^bb8:  // pred: ^bb6
    %24 = llvm.mlir.addressof @str0 : !llvm.ptr
    %25 = llvm.getelementptr %24[0, 0] : (!llvm.ptr) -> !llvm.ptr, !llvm.array<11 x i8>
    %26 = llvm.call @printf(%25, %13) vararg(!llvm.func<i32 (ptr, ...)>) : (!llvm.ptr, i32) -> i32
    %27 = llvm.mlir.zero : !llvm.ptr
    %28 = llvm.mlir.addressof @str1 : !llvm.ptr
    %29 = llvm.getelementptr %28[0, 0] : (!llvm.ptr) -> !llvm.ptr, !llvm.array<7 x i8>
    %30 = llvm.sext %13 : i32 to i64
    llvm.br ^bb9(%6, %14 : i32, !llvm.ptr)
  ^bb9(%31: i32, %32: !llvm.ptr):  // 2 preds: ^bb8, ^bb10
    %33 = llvm.icmp "ne" %32, %27 : !llvm.ptr
    llvm.cond_br %33, ^bb10(%31, %32 : i32, !llvm.ptr), ^bb11
  ^bb10(%34: i32, %35: !llvm.ptr):  // pred: ^bb9
    %36 = llvm.call @_ZN4Node7get_keyEv(%35) : (!llvm.ptr) -> i32
    %37 = llvm.call @printf(%29, %36) vararg(!llvm.func<i32 (ptr, ...)>) : (!llvm.ptr, i32) -> i32
    %38 = llvm.getelementptr %35[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (struct<(i32, array<1024 x i8>)>, struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>, struct<"opaque@polygeist@<EMAIL>::mutex", (struct<"opaque@polygeist@<EMAIL>::__mutex_base", (struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>)>)>, i8, i8, i32)>
    %39 = llvm.call @_ZN6MyListIP4NodeEixEm(%38, %30) : (!llvm.ptr, i64) -> !llvm.ptr
    %40 = llvm.load %39 : !llvm.ptr -> !llvm.ptr
    %41 = llvm.add %34, %2  : i32
    llvm.br ^bb9(%41, %40 : i32, !llvm.ptr)
  ^bb11:  // pred: ^bb9
    %42 = llvm.icmp "ne" %31, %1 : i32
    %43 = llvm.mlir.addressof @_ZSt4cout : !llvm.ptr
    %44 = llvm.mlir.addressof @_ZSt4endlIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_ : !llvm.ptr
    %45 = llvm.call @_ZNSolsEPFRSoS_E(%43, %44) : (!llvm.ptr, !llvm.ptr) -> !llvm.ptr
    llvm.br ^bb12(%42 : i1)
  ^bb12(%46: i1):  // 2 preds: ^bb7, ^bb11
    llvm.br ^bb13
  ^bb13:  // pred: ^bb12
    llvm.cond_br %46, ^bb14, ^bb15
  ^bb14:  // pred: ^bb13
    %47 = llvm.add %13, %2  : i32
    llvm.br ^bb16(%47 : i32)
  ^bb15:  // pred: ^bb13
    llvm.br ^bb16(%13 : i32)
  ^bb16(%48: i32):  // 2 preds: ^bb14, ^bb15
    llvm.br ^bb17
  ^bb17:  // pred: ^bb16
    llvm.br ^bb1(%48, %46 : i32, i1)
  ^bb18:  // pred: ^bb1
    %49 = llvm.mlir.addressof @str2 : !llvm.ptr
    %50 = llvm.getelementptr %49[0, 0] : (!llvm.ptr) -> !llvm.ptr, !llvm.array<38 x i8>
    %51 = llvm.call @printf(%50) vararg(!llvm.func<i32 (ptr, ...)>) : (!llvm.ptr) -> i32
    llvm.return
  }
  llvm.func available_externally @_ZNSolsEPFRSoS_E(%arg0: !llvm.ptr, %arg1: !llvm.ptr) -> !llvm.ptr {
    %0 = llvm.call %arg1(%arg0) : !llvm.ptr, (!llvm.ptr) -> !llvm.ptr
    llvm.return %0 : !llvm.ptr
  }
  llvm.func available_externally @_ZSt4endlIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_(%arg0: !llvm.ptr) -> !llvm.ptr {
    %0 = llvm.mlir.constant(10 : i8) : i8
    %1 = llvm.getelementptr %arg0[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(ptr, struct<(struct<(ptr, i64, i64, i32, i32, i32, ptr, struct<(ptr, i64)>, array<8 x struct<(ptr, i64)>>, i32, ptr, struct<(ptr)>)>, ptr, i8, i8, ptr, ptr, ptr, ptr)>)>
    %2 = llvm.call @_ZNKSt9basic_iosIcSt11char_traitsIcEE5widenEc(%1, %0) : (!llvm.ptr, i8) -> i8
    %3 = llvm.call @_ZNSo3putEc(%arg0, %2) : (!llvm.ptr, i8) -> !llvm.ptr
    %4 = llvm.call @_ZNSo5flushEv(%3) : (!llvm.ptr) -> !llvm.ptr
    llvm.return %4 : !llvm.ptr
  }
  llvm.func @_ZN8SkipListC1Ev(%arg0: !llvm.ptr) {
    llvm.return
  }
  llvm.func @_ZN8SkipListD1Ev(%arg0: !llvm.ptr) {
    llvm.return
  }
  llvm.func @pth_bm_target_create() -> !llvm.ptr {
    %0 = llvm.mlir.constant(16 : i64) : i64
    %1 = llvm.mlir.constant(5.000000e-01 : f32) : f32
    %2 = llvm.mlir.constant(1048576 : i32) : i32
    %3 = llvm.mlir.constant(1 : index) : i64
    %4 = llvm.mul %0, %3  : i64
    %5 = llvm.call @malloc(%4) : (i64) -> !llvm.ptr
    llvm.call @_ZN8SkipListC1Eif(%5, %2, %1) : (!llvm.ptr, i32, f32) -> ()
    llvm.return %5 : !llvm.ptr
  }
  llvm.func @pth_bm_target_destroy(%arg0: !llvm.ptr) {
    llvm.call @free(%arg0) : (!llvm.ptr) -> ()
    llvm.return
  }
  llvm.func @pth_bm_target_read(%arg0: !llvm.ptr, %arg1: i32) {
    %0 = llvm.call @_ZN8SkipList6searchEi(%arg0, %arg1) : (!llvm.ptr, i32) -> !llvm.ptr
    llvm.return
  }
  llvm.func @pth_bm_target_insert(%arg0: !llvm.ptr, %arg1: i32) {
    %0 = llvm.mlir.zero : !llvm.ptr
    %1 = llvm.call @_ZN8SkipList3addEiPKh(%arg0, %arg1, %0) : (!llvm.ptr, i32, !llvm.ptr) -> i8
    llvm.return
  }
  llvm.func @pth_bm_target_update(%arg0: !llvm.ptr, %arg1: i32) {
    llvm.return
  }
  llvm.func @pth_bm_target_delete(%arg0: !llvm.ptr, %arg1: i32) {
    %0 = llvm.call @_ZN8SkipList6removeEi(%arg0, %arg1) : (!llvm.ptr, i32) -> i8
    llvm.return
  }
  llvm.func linkonce_odr @_ZN12KeyValuePairC1EiPKh(%arg0: !llvm.ptr, %arg1: i32, %arg2: !llvm.ptr) {
    %0 = llvm.mlir.constant(1 : index) : i64
    %1 = llvm.mlir.constant(0 : i8) : i8
    %2 = llvm.mlir.constant(1024 : index) : i64
    %3 = llvm.mlir.constant(0 : index) : i64
    llvm.store %arg1, %arg0 : i32, !llvm.ptr
    %4 = llvm.mlir.zero : !llvm.ptr
    %5 = llvm.icmp "ne" %arg2, %4 : !llvm.ptr
    llvm.cond_br %5, ^bb1, ^bb5
  ^bb1:  // pred: ^bb0
    %6 = llvm.getelementptr %arg0[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(i32, array<1024 x i8>)>
    llvm.br ^bb2(%3 : i64)
  ^bb2(%7: i64):  // 2 preds: ^bb1, ^bb3
    %8 = llvm.icmp "slt" %7, %2 : i64
    llvm.cond_br %8, ^bb3, ^bb4
  ^bb3:  // pred: ^bb2
    %9 = llvm.getelementptr %arg2[%7] : (!llvm.ptr, i64) -> !llvm.ptr, i8
    %10 = llvm.load %9 : !llvm.ptr -> i8
    %11 = llvm.trunc %7 : i64 to i32
    %12 = llvm.getelementptr %6[%11] : (!llvm.ptr, i32) -> !llvm.ptr, i8
    llvm.store %10, %12 : i8, !llvm.ptr
    %13 = llvm.add %7, %0  : i64
    llvm.br ^bb2(%13 : i64)
  ^bb4:  // 2 preds: ^bb2, ^bb6
    llvm.br ^bb8
  ^bb5:  // pred: ^bb0
    %14 = llvm.getelementptr %arg0[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(i32, array<1024 x i8>)>
    llvm.br ^bb6(%3 : i64)
  ^bb6(%15: i64):  // 2 preds: ^bb5, ^bb7
    %16 = llvm.icmp "slt" %15, %2 : i64
    llvm.cond_br %16, ^bb7, ^bb4
  ^bb7:  // pred: ^bb6
    %17 = llvm.trunc %15 : i64 to i32
    %18 = llvm.getelementptr %14[%17] : (!llvm.ptr, i32) -> !llvm.ptr, i8
    llvm.store %1, %18 : i8, !llvm.ptr
    %19 = llvm.add %15, %0  : i64
    llvm.br ^bb6(%19 : i64)
  ^bb8:  // pred: ^bb4
    llvm.return
  }
  llvm.func linkonce_odr @_ZN6MyListIP4NodeEC1Ev(%arg0: !llvm.ptr) {
    %0 = llvm.mlir.constant(0 : i64) : i64
    %1 = llvm.mlir.zero : !llvm.ptr
    llvm.store %1, %arg0 : !llvm.ptr, !llvm.ptr
    %2 = llvm.getelementptr %arg0[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>
    llvm.store %1, %2 : !llvm.ptr, !llvm.ptr
    %3 = llvm.getelementptr %arg0[0, 2] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>
    llvm.store %0, %3 : i64, !llvm.ptr
    llvm.return
  }
  llvm.func linkonce_odr @_ZNSt5mutexC1Ev(%arg0: !llvm.ptr) {
    llvm.call @_ZNSt12__mutex_baseC1Ev(%arg0) : (!llvm.ptr) -> ()
    llvm.return
  }
  llvm.func linkonce_odr @_ZN6MyListIP4NodeE4pushES1_(%arg0: !llvm.ptr, %arg1: !llvm.ptr) {
    %0 = llvm.mlir.constant(16 : i64) : i64
    %1 = llvm.mlir.constant(1 : i64) : i64
    %2 = llvm.mlir.constant(0 : i8) : i8
    %3 = llvm.mlir.constant(1 : index) : i64
    %4 = llvm.mul %0, %3  : i64
    %5 = llvm.call @malloc(%4) : (i64) -> !llvm.ptr
    llvm.call @_ZN10MyListNodeIP4NodeEC1ES1_(%5, %arg1) : (!llvm.ptr, !llvm.ptr) -> ()
    %6 = llvm.call @_ZN6MyListIP4NodeE8is_emptyEv(%arg0) : (!llvm.ptr) -> i8
    %7 = llvm.icmp "ne" %6, %2 : i8
    llvm.cond_br %7, ^bb1, ^bb2
  ^bb1:  // pred: ^bb0
    %8 = llvm.getelementptr %arg0[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>
    llvm.store %5, %8 : !llvm.ptr, !llvm.ptr
    %9 = llvm.load %8 : !llvm.ptr -> !llvm.ptr
    llvm.store %9, %arg0 : !llvm.ptr, !llvm.ptr
    llvm.br ^bb3
  ^bb2:  // pred: ^bb0
    %10 = llvm.getelementptr %arg0[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>
    %11 = llvm.load %10 : !llvm.ptr -> !llvm.ptr
    %12 = llvm.getelementptr %11[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr)>
    llvm.store %5, %12 : !llvm.ptr, !llvm.ptr
    llvm.store %5, %10 : !llvm.ptr, !llvm.ptr
    llvm.br ^bb3
  ^bb3:  // 2 preds: ^bb1, ^bb2
    %13 = llvm.getelementptr %arg0[0, 2] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>
    %14 = llvm.load %13 : !llvm.ptr -> i64
    %15 = llvm.add %14, %1  : i64
    llvm.store %15, %13 : i64, !llvm.ptr
    llvm.return
  }
  llvm.func linkonce_odr @_ZNK12KeyValuePair7get_keyEv(%arg0: !llvm.ptr) -> i32 {
    %0 = llvm.load %arg0 : !llvm.ptr -> i32
    llvm.return %0 : i32
  }
  llvm.func linkonce_odr @_ZNSt5mutex4lockEv(%arg0: !llvm.ptr) {
    %0 = llvm.mlir.constant(0 : i32) : i32
    %1 = llvm.call @pthread_mutex_lock(%arg0) : (!llvm.ptr) -> i32
    %2 = llvm.icmp "ne" %1, %0 : i32
    llvm.cond_br %2, ^bb1, ^bb2
  ^bb1:  // pred: ^bb0
    llvm.call @_ZSt20__throw_system_errori(%1) : (i32) -> ()
    llvm.br ^bb2
  ^bb2:  // 2 preds: ^bb0, ^bb1
    llvm.return
  }
  llvm.func linkonce_odr @_ZNSt5mutex6unlockEv(%arg0: !llvm.ptr) {
    %0 = llvm.call @pthread_mutex_unlock(%arg0) : (!llvm.ptr) -> i32
    llvm.return
  }
  llvm.func linkonce_odr @_ZN12KeyValuePair9get_valueEv(%arg0: !llvm.ptr) -> !llvm.ptr {
    %0 = llvm.getelementptr %arg0[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(i32, array<1024 x i8>)>
    llvm.return %0 : !llvm.ptr
  }
  llvm.func available_externally @_ZSt5flushIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_(%arg0: !llvm.ptr) -> !llvm.ptr {
    %0 = llvm.call @_ZNSo5flushEv(%arg0) : (!llvm.ptr) -> !llvm.ptr
    llvm.return %0 : !llvm.ptr
  }
  llvm.func @_ZNSo3putEc(!llvm.ptr, i8) -> !llvm.ptr attributes {sym_visibility = "private"}
  llvm.func available_externally @_ZNKSt9basic_iosIcSt11char_traitsIcEE5widenEc(%arg0: !llvm.ptr, %arg1: i8) -> i8 {
    %0 = llvm.getelementptr %arg0[0, 5] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(struct<(ptr, i64, i64, i32, i32, i32, ptr, struct<(ptr, i64)>, array<8 x struct<(ptr, i64)>>, i32, ptr, struct<(ptr)>)>, ptr, i8, i8, ptr, ptr, ptr, ptr)>
    %1 = llvm.load %0 : !llvm.ptr -> !llvm.ptr
    %2 = llvm.call @_ZSt13__check_facetISt5ctypeIcEERKT_PS3_(%1) : (!llvm.ptr) -> !llvm.ptr
    %3 = llvm.call @_ZNKSt5ctypeIcE5widenEc(%2, %arg1) : (!llvm.ptr, i8) -> i8
    llvm.return %3 : i8
  }
  llvm.func linkonce_odr @_ZNSt12__mutex_baseC1Ev(%arg0: !llvm.ptr) {
    %0 = llvm.mlir.constant(0 : i32) : i32
    %1 = llvm.mlir.constant(0 : i16) : i16
    %2 = llvm.mlir.constant(1 : index) : i64
    %3 = llvm.alloca %2 x !llvm.struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)> : (i64) -> !llvm.ptr
    %4 = llvm.getelementptr %3[0, 0] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>
    %5 = llvm.getelementptr %4[0, 0] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>
    llvm.store %0, %5 : i32, !llvm.ptr
    %6 = llvm.getelementptr %4[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>
    llvm.store %0, %6 : i32, !llvm.ptr
    %7 = llvm.getelementptr %4[0, 2] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>
    llvm.store %0, %7 : i32, !llvm.ptr
    %8 = llvm.getelementptr %4[0, 3] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>
    llvm.store %0, %8 : i32, !llvm.ptr
    %9 = llvm.getelementptr %4[0, 4] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>
    llvm.store %0, %9 : i32, !llvm.ptr
    %10 = llvm.getelementptr %4[0, 5] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>
    llvm.store %1, %10 : i16, !llvm.ptr
    %11 = llvm.getelementptr %4[0, 6] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>
    llvm.store %1, %11 : i16, !llvm.ptr
    %12 = llvm.getelementptr %4[0, 7] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>
    %13 = llvm.getelementptr %12[0, 0] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(ptr, ptr)>
    %14 = llvm.mlir.zero : !llvm.ptr
    llvm.store %14, %13 : !llvm.ptr, !llvm.ptr
    %15 = llvm.getelementptr %12[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<(ptr, ptr)>
    llvm.store %14, %15 : !llvm.ptr, !llvm.ptr
    %16 = llvm.load %3 : !llvm.ptr -> !llvm.struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>
    llvm.store %16, %arg0 : !llvm.struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>, !llvm.ptr
    %17 = llvm.load %arg0 : !llvm.ptr -> !llvm.struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>
    llvm.store %17, %arg0 : !llvm.struct<(struct<(i32, i32, i32, i32, i32, i16, i16, struct<(ptr, ptr)>)>)>, !llvm.ptr
    llvm.return
  }
  llvm.func linkonce_odr @_ZN10MyListNodeIP4NodeEC1ES1_(%arg0: !llvm.ptr, %arg1: !llvm.ptr) {
    llvm.store %arg1, %arg0 : !llvm.ptr, !llvm.ptr
    %0 = llvm.mlir.zero : !llvm.ptr
    %1 = llvm.getelementptr %arg0[0, 1] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr)>
    llvm.store %0, %1 : !llvm.ptr, !llvm.ptr
    llvm.return
  }
  llvm.func linkonce_odr @_ZN6MyListIP4NodeE8is_emptyEv(%arg0: !llvm.ptr) -> i8 {
    %0 = llvm.mlir.constant(0 : i64) : i64
    %1 = llvm.getelementptr %arg0[0, 2] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<"_Converted_opaque@polygeist@<EMAIL>", (ptr, ptr, i64)>
    %2 = llvm.load %1 : !llvm.ptr -> i64
    %3 = llvm.icmp "eq" %2, %0 : i64
    %4 = llvm.zext %3 : i1 to i8
    llvm.return %4 : i8
  }
  llvm.func @_ZSt20__throw_system_errori(i32) attributes {sym_visibility = "private"}
  llvm.func @_ZNSo5flushEv(!llvm.ptr) -> !llvm.ptr attributes {sym_visibility = "private"}
  llvm.func linkonce_odr @_ZNKSt5ctypeIcE5widenEc(%arg0: !llvm.ptr, %arg1: i8) -> i8 {
    %0 = llvm.mlir.constant(0 : i8) : i8
    %1 = llvm.mlir.undef : i8
    %2 = llvm.getelementptr %arg0[0, 8] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<packed (struct<packed (ptr, i32)>, array<4 x i8>, ptr, i8, array<7 x i8>, ptr, ptr, ptr, i8, array<256 x i8>, array<256 x i8>, i8, array<6 x i8>)>
    %3 = llvm.load %2 : !llvm.ptr -> i8
    %4 = llvm.icmp "ne" %3, %0 : i8
    %5 = llvm.icmp "eq" %3, %0 : i8
    llvm.cond_br %5, ^bb1, ^bb2
  ^bb1:  // pred: ^bb0
    llvm.call @_ZNKSt5ctypeIcE13_M_widen_initEv(%arg0) : (!llvm.ptr) -> ()
    llvm.br ^bb7(%arg1 : i8)
  ^bb2:  // pred: ^bb0
    llvm.cond_br %4, ^bb3, ^bb4
  ^bb3:  // pred: ^bb2
    %6 = llvm.getelementptr %arg0[0, 9] : (!llvm.ptr) -> !llvm.ptr, !llvm.struct<packed (struct<packed (ptr, i32)>, array<4 x i8>, ptr, i8, array<7 x i8>, ptr, ptr, ptr, i8, array<256 x i8>, array<256 x i8>, i8, array<6 x i8>)>
    %7 = llvm.sext %arg1 : i8 to i64
    %8 = llvm.trunc %7 : i64 to i32
    %9 = llvm.getelementptr %6[%8] : (!llvm.ptr, i32) -> !llvm.ptr, i8
    %10 = llvm.load %9 : !llvm.ptr -> i8
    llvm.br ^bb5(%10 : i8)
  ^bb4:  // pred: ^bb2
    llvm.br ^bb5(%1 : i8)
  ^bb5(%11: i8):  // 2 preds: ^bb3, ^bb4
    llvm.br ^bb6
  ^bb6:  // pred: ^bb5
    llvm.br ^bb7(%11 : i8)
  ^bb7(%12: i8):  // 2 preds: ^bb1, ^bb6
    llvm.br ^bb8
  ^bb8:  // pred: ^bb7
    llvm.return %12 : i8
  }
  llvm.func linkonce_odr @_ZSt13__check_facetISt5ctypeIcEERKT_PS3_(%arg0: !llvm.ptr) -> !llvm.ptr {
    %0 = llvm.mlir.constant(true) : i1
    %1 = llvm.mlir.zero : !llvm.ptr
    %2 = llvm.icmp "ne" %arg0, %1 : !llvm.ptr
    %3 = llvm.xor %2, %0  : i1
    llvm.cond_br %3, ^bb1, ^bb2
  ^bb1:  // pred: ^bb0
    llvm.call @_ZSt16__throw_bad_castv() : () -> ()
    llvm.br ^bb2
  ^bb2:  // 2 preds: ^bb0, ^bb1
    llvm.return %arg0 : !llvm.ptr
  }
  llvm.func @pthread_mutex_lock(!llvm.ptr) -> i32 attributes {sym_visibility = "private"}
  llvm.func @pthread_mutex_unlock(!llvm.ptr) -> i32 attributes {sym_visibility = "private"}
  llvm.func @_ZNKSt5ctypeIcE13_M_widen_initEv(!llvm.ptr) attributes {sym_visibility = "private"}
  llvm.func linkonce_odr @_ZNKSt5ctypeIcE8do_widenEc(%arg0: !llvm.ptr, %arg1: i8) -> i8 {
    llvm.return %arg1 : i8
  }
  llvm.func @_ZSt16__throw_bad_castv() attributes {sym_visibility = "private"}
}
