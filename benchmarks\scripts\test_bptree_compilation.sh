#!/bin/bash

# Test script for B+ tree compilation
# This script verifies that the compilation pipeline works correctly

set -e  # Exit on any error

SCRIPT_DIR="$(dirname "$0")"
OUTPUT_DIR="$SCRIPT_DIR/output"
BPTREE_DIR="$SCRIPT_DIR/../bptree"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  B+ Tree Compilation Test${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if source file exists
    if [[ ! -f "$BPTREE_DIR/bplus_tree.c" ]]; then
        print_error "Source file not found: $BPTREE_DIR/bplus_tree.c"
        return 1
    fi
    print_success "Source file found: $BPTREE_DIR/bplus_tree.c"
    
    # Check if header file exists
    if [[ ! -f "$BPTREE_DIR/bplus_tree.h" ]]; then
        print_error "Header file not found: $BPTREE_DIR/bplus_tree.h"
        return 1
    fi
    print_success "Header file found: $BPTREE_DIR/bplus_tree.h"
    
    # Check if compilation scripts exist
    if [[ ! -f "$SCRIPT_DIR/compile_bptree.sh" ]]; then
        print_error "Compilation script not found: compile_bptree.sh"
        return 1
    fi
    print_success "Regular compilation script found"
    
    if [[ ! -f "$SCRIPT_DIR/compile_bptree_optimized.sh" ]]; then
        print_error "Optimized compilation script not found: compile_bptree_optimized.sh"
        return 1
    fi
    print_success "Optimized compilation script found"
    
    return 0
}

check_tools() {
    print_info "Checking tool availability..."
    
    # Define tool paths (same as in compilation scripts)
    POLYGEIST_PATH="${POLYGEIST_PATH:-$HOME/Polygeist/build}"
    LLVM_PROJECT_PATH="${LLVM_PROJECT_PATH:-$HOME/Polygeist/llvm-project/build}"
    COMPILER_BUILD_PATH="$SCRIPT_DIR/../../compiler/build"
    
    TOOLS=(
        "$POLYGEIST_PATH/bin/cgeist"
        "$COMPILER_BUILD_PATH/bin/my-opt"
        "$LLVM_PROJECT_PATH/bin/mlir-translate"
        "$LLVM_PROJECT_PATH/bin/llc"
    )
    
    local missing_tools=0
    for tool in "${TOOLS[@]}"; do
        if [[ -x "$tool" ]]; then
            print_success "Found: $(basename "$tool")"
        else
            print_warning "Missing or not executable: $tool"
            ((missing_tools++))
        fi
    done
    
    if [[ $missing_tools -gt 0 ]]; then
        print_warning "$missing_tools tools are missing or not executable"
        print_info "This test will check script syntax but may not run full compilation"
        return 1
    else
        print_success "All required tools found"
        return 0
    fi
}

test_script_syntax() {
    print_info "Testing script syntax..."
    
    # Test regular compilation script
    if bash -n "$SCRIPT_DIR/compile_bptree.sh"; then
        print_success "Regular compilation script syntax OK"
    else
        print_error "Regular compilation script has syntax errors"
        return 1
    fi
    
    # Test optimized compilation script
    if bash -n "$SCRIPT_DIR/compile_bptree_optimized.sh"; then
        print_success "Optimized compilation script syntax OK"
    else
        print_error "Optimized compilation script has syntax errors"
        return 1
    fi
    
    # Test build script
    if [[ -f "$SCRIPT_DIR/build_bptree.sh" ]]; then
        if bash -n "$SCRIPT_DIR/build_bptree.sh"; then
            print_success "Build script syntax OK"
        else
            print_error "Build script has syntax errors"
            return 1
        fi
    fi
    
    return 0
}

analyze_source_code() {
    print_info "Analyzing B+ tree source code..."
    
    local source_file="$BPTREE_DIR/bplus_tree.c"
    local header_file="$BPTREE_DIR/bplus_tree.h"
    
    # Count lines of code
    local c_lines=$(wc -l < "$source_file")
    local h_lines=$(wc -l < "$header_file")
    print_info "Source code: $c_lines lines (C), $h_lines lines (header)"
    
    # Check for key functions
    local functions=(
        "bplus_tree_create"
        "bplus_tree_insert"
        "bplus_tree_search"
        "bplus_tree_delete"
        "bplus_tree_concurrent_insert"
        "bplus_tree_concurrent_search"
        "bplus_tree_concurrent_delete"
        "pth_bm_target_create"
        "pth_bm_target_read"
        "pth_bm_target_insert"
    )
    
    print_info "Checking for key functions..."
    for func in "${functions[@]}"; do
        if grep -q "$func" "$source_file"; then
            print_success "Found function: $func"
        else
            print_warning "Function not found: $func"
        fi
    done
    
    # Check for thread safety features
    if grep -q "pthread" "$header_file"; then
        print_success "Thread safety features detected (pthread)"
    else
        print_warning "No pthread usage detected"
    fi
    
    if grep -q "rwlock" "$source_file"; then
        print_success "Read-write locks detected"
    else
        print_warning "No read-write locks detected"
    fi
}

test_dry_run() {
    print_info "Testing compilation scripts (dry run)..."
    
    # Create a temporary output directory for testing
    local test_output="$SCRIPT_DIR/test_output"
    mkdir -p "$test_output"
    
    # Test if scripts can at least start (they'll fail on missing tools, but that's OK)
    print_info "Testing regular compilation script..."
    if timeout 10s bash "$SCRIPT_DIR/compile_bptree.sh" 2>/dev/null || true; then
        print_info "Regular script executed (may have failed on missing tools)"
    fi
    
    print_info "Testing optimized compilation script..."
    if timeout 10s bash "$SCRIPT_DIR/compile_bptree_optimized.sh" 2>/dev/null || true; then
        print_info "Optimized script executed (may have failed on missing tools)"
    fi
    
    # Clean up test output
    rm -rf "$test_output" 2>/dev/null || true
}

show_summary() {
    print_info "Test Summary:"
    echo "  - B+ tree source: $BPTREE_DIR/bplus_tree.c"
    echo "  - Compilation scripts: compile_bptree.sh, compile_bptree_optimized.sh"
    echo "  - Build script: build_bptree.sh"
    echo "  - Output directory: $OUTPUT_DIR"
    echo
    print_info "To run compilation:"
    echo "  cd $SCRIPT_DIR"
    echo "  ./build_bptree.sh both"
    echo
    print_info "To test B+ tree functionality:"
    echo "  cd $BPTREE_DIR"
    echo "  make && ./test_simple"
}

# Main test execution
print_header

echo "Testing B+ tree compilation setup..."
echo

# Run all tests
if check_prerequisites; then
    print_success "Prerequisites check passed"
else
    print_error "Prerequisites check failed"
    exit 1
fi

echo

tools_available=true
if ! check_tools; then
    tools_available=false
fi

echo

if test_script_syntax; then
    print_success "Script syntax check passed"
else
    print_error "Script syntax check failed"
    exit 1
fi

echo

analyze_source_code

echo

if [[ "$tools_available" == "true" ]]; then
    test_dry_run
else
    print_warning "Skipping dry run test due to missing tools"
fi

echo

show_summary

print_success "B+ tree compilation test completed successfully!"
